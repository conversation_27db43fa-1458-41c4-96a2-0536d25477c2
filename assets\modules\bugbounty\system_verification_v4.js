/**
 * Bug Bounty System v4.0 - التحقق النهائي من النظام
 * ملف التحقق الشامل للتأكد من عمل جميع المكونات بشكل صحيح
 */

class SystemVerificationV4 {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Bug Bounty System v4.0 Verification';
        this.verificationResults = [];
        this.criticalIssues = [];
        this.warnings = [];
        this.passed = 0;
        this.failed = 0;
        
        console.log(`🔍 ${this.systemName} تم تحميله بنجاح`);
    }
    
    // التحقق الشامل من النظام
    async performCompleteVerification() {
        console.log('🔍 بدء التحقق الشامل من النظام v4.0...');
        
        // إعادة تعيين النتائج
        this.resetResults();
        
        // 1. التحقق من الملفات الأساسية
        await this.verifyCoreFiles();
        
        // 2. التحقق من الكلاسات والدوال
        await this.verifyClassesAndFunctions();
        
        // 3. التحقق من التكامل
        await this.verifyIntegration();
        
        // 4. التحقق من الميزات الجديدة v4.0
        await this.verifyV4Features();
        
        // 5. التحقق من الأداء
        await this.verifyPerformance();
        
        // 6. التحقق من الأمان
        await this.verifySecurity();
        
        // عرض النتائج النهائية
        return this.displayVerificationResults();
    }
    
    // إعادة تعيين النتائج
    resetResults() {
        this.verificationResults = [];
        this.criticalIssues = [];
        this.warnings = [];
        this.passed = 0;
        this.failed = 0;
    }
    
    // التحقق من الملفات الأساسية
    async verifyCoreFiles() {
        console.log('📁 التحقق من الملفات الأساسية...');
        
        const coreFiles = [
            { name: 'BugBountyCore.js', required: true },
            { name: 'impact_visualizer.js', required: true },
            { name: 'prompt_only_system.js', required: true },
            { name: 'report_exporter.js', required: true },
            { name: 'test_system.js', required: true },
            { name: 'system_config_v4.js', required: true },
            { name: 'prompt_template.txt', required: true },
            { name: 'analyzer.py', required: false }
        ];
        
        for (const file of coreFiles) {
            try {
                const response = await fetch(`assets/modules/bugbounty/${file.name}`);
                const exists = response.ok;
                
                this.addVerification(
                    `File: ${file.name}`,
                    exists,
                    file.required ? 'critical' : 'warning',
                    exists ? 'ملف موجود ومتاح' : 'ملف مفقود'
                );
                
                if (!exists && file.required) {
                    this.criticalIssues.push(`ملف أساسي مفقود: ${file.name}`);
                }
            } catch (error) {
                this.addVerification(
                    `File: ${file.name}`,
                    false,
                    'critical',
                    `خطأ في الوصول: ${error.message}`
                );
            }
        }
    }
    
    // التحقق من الكلاسات والدوال
    async verifyClassesAndFunctions() {
        console.log('🏗️ التحقق من الكلاسات والدوال...');
        
        const requiredClasses = [
            'BugBountyCore',
            'ImpactVisualizer',
            'PromptOnlyBugBountySystem',
            'BugBountyReportExporter',
            'BugBountySystemTester',
            'BugBountySystemConfig'
        ];
        
        const requiredFunctions = [
            'startComprehensiveBugBountyScan',
            'getSystemConfig',
            'checkSystemCompatibility',
            'runBugBountyTests'
        ];
        
        // فحص الكلاسات
        requiredClasses.forEach(className => {
            const exists = typeof window[className] === 'function';
            this.addVerification(
                `Class: ${className}`,
                exists,
                'critical',
                exists ? 'كلاس محمل ومتاح' : 'كلاس مفقود'
            );
            
            if (!exists) {
                this.criticalIssues.push(`كلاس أساسي مفقود: ${className}`);
            }
        });
        
        // فحص الدوال
        requiredFunctions.forEach(funcName => {
            const exists = typeof window[funcName] === 'function';
            this.addVerification(
                `Function: ${funcName}`,
                exists,
                'critical',
                exists ? 'دالة محملة ومتاحة' : 'دالة مفقودة'
            );
            
            if (!exists) {
                this.criticalIssues.push(`دالة أساسية مفقودة: ${funcName}`);
            }
        });
    }
    
    // التحقق من التكامل
    async verifyIntegration() {
        console.log('🔗 التحقق من التكامل...');
        
        try {
            // اختبار إنشاء مثيل من BugBountyCore
            if (typeof window.BugBountyCore === 'function') {
                const core = new window.BugBountyCore();
                const hasRequiredMethods = [
                    'collectComprehensiveWebsiteData',
                    'performAIVulnerabilityAnalysis',
                    'generateProfessionalAnalysis',
                    'loadPromptTemplate'
                ].every(method => typeof core[method] === 'function');
                
                this.addVerification(
                    'BugBountyCore Integration',
                    hasRequiredMethods,
                    'critical',
                    hasRequiredMethods ? 'تكامل صحيح' : 'تكامل ناقص'
                );
            }
            
            // اختبار تحميل البرومبت
            if (typeof window.BugBountyCore === 'function') {
                const core = new window.BugBountyCore();
                try {
                    const prompt = await core.loadPromptTemplate();
                    const promptValid = prompt && prompt.length > 100;
                    
                    this.addVerification(
                        'Prompt Template Loading',
                        promptValid,
                        'critical',
                        promptValid ? 'البرومبت محمل بنجاح' : 'البرومبت فارغ أو مفقود'
                    );
                } catch (error) {
                    this.addVerification(
                        'Prompt Template Loading',
                        false,
                        'critical',
                        `خطأ في تحميل البرومبت: ${error.message}`
                    );
                }
            }
            
        } catch (error) {
            this.addVerification(
                'Integration Test',
                false,
                'critical',
                `خطأ في اختبار التكامل: ${error.message}`
            );
        }
    }
    
    // التحقق من ميزات v4.0
    async verifyV4Features() {
        console.log('✨ التحقق من ميزات v4.0...');
        
        // التحقق من الاستغلال الحقيقي
        const realExploitationSupport = typeof window.ImpactVisualizer === 'function' &&
            typeof window.PromptOnlyBugBountySystem === 'function';
        
        this.addVerification(
            'Real Exploitation Support',
            realExploitationSupport,
            'important',
            realExploitationSupport ? 'دعم الاستغلال الحقيقي متاح' : 'دعم الاستغلال الحقيقي مفقود'
        );
        
        // التحقق من الصور الحقيقية
        const screenshotSupport = !!document.createElement('canvas').getContext;
        
        this.addVerification(
            'Screenshot Support',
            screenshotSupport,
            'important',
            screenshotSupport ? 'دعم الصور متاح' : 'دعم الصور مفقود'
        );
        
        // التحقق من التقارير المتعددة
        const reportExportSupport = typeof window.BugBountyReportExporter === 'function';
        
        this.addVerification(
            'Multi-format Reports',
            reportExportSupport,
            'important',
            reportExportSupport ? 'دعم التقارير المتعددة متاح' : 'دعم التقارير المتعددة مفقود'
        );
    }
    
    // التحقق من الأداء
    async verifyPerformance() {
        console.log('⚡ التحقق من الأداء...');
        
        // اختبار سرعة التحميل
        const startTime = performance.now();
        
        try {
            if (typeof window.BugBountyCore === 'function') {
                const core = new window.BugBountyCore();
                await core.loadPromptTemplate();
            }
            
            const loadTime = performance.now() - startTime;
            const performanceGood = loadTime < 5000; // أقل من 5 ثوان
            
            this.addVerification(
                'Performance Test',
                performanceGood,
                'warning',
                `وقت التحميل: ${Math.round(loadTime)}ms`
            );
            
        } catch (error) {
            this.addVerification(
                'Performance Test',
                false,
                'warning',
                `خطأ في اختبار الأداء: ${error.message}`
            );
        }
    }
    
    // التحقق من الأمان
    async verifySecurity() {
        console.log('🔒 التحقق من الأمان...');
        
        // التحقق من HTTPS
        const isHTTPS = location.protocol === 'https:';
        
        this.addVerification(
            'HTTPS Protocol',
            isHTTPS,
            'warning',
            isHTTPS ? 'اتصال آمن' : 'اتصال غير آمن'
        );
        
        // التحقق من CSP
        const hasCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null;
        
        this.addVerification(
            'Content Security Policy',
            hasCSP,
            'warning',
            hasCSP ? 'CSP مُفعل' : 'CSP غير مُفعل'
        );
    }
    
    // إضافة نتيجة تحقق
    addVerification(name, passed, level, message) {
        const result = {
            name,
            passed,
            level,
            message,
            timestamp: new Date().toISOString()
        };
        
        this.verificationResults.push(result);
        
        if (passed) {
            this.passed++;
            console.log(`✅ ${name}: ${message}`);
        } else {
            this.failed++;
            console.log(`❌ ${name}: ${message}`);
            
            if (level === 'critical') {
                this.criticalIssues.push(`${name}: ${message}`);
            } else if (level === 'warning') {
                this.warnings.push(`${name}: ${message}`);
            }
        }
    }
    
    // عرض النتائج النهائية
    displayVerificationResults() {
        console.log('\n' + '='.repeat(80));
        console.log('🔍 نتائج التحقق من Bug Bounty System v4.0');
        console.log('='.repeat(80));
        
        const total = this.verificationResults.length;
        const successRate = Math.round((this.passed / total) * 100);
        
        console.log(`📊 إجمالي الفحوصات: ${total}`);
        console.log(`✅ نجح: ${this.passed}`);
        console.log(`❌ فشل: ${this.failed}`);
        console.log(`📈 معدل النجاح: ${successRate}%`);
        
        if (this.criticalIssues.length > 0) {
            console.log('\n🚨 مشاكل حرجة:');
            this.criticalIssues.forEach(issue => console.log(`  - ${issue}`));
        }
        
        if (this.warnings.length > 0) {
            console.log('\n⚠️ تحذيرات:');
            this.warnings.forEach(warning => console.log(`  - ${warning}`));
        }
        
        console.log('\n' + '='.repeat(80));
        
        let status = 'unknown';
        let statusMessage = '';
        
        if (this.criticalIssues.length === 0 && successRate >= 90) {
            status = 'excellent';
            statusMessage = '🎉 النظام v4.0 يعمل بشكل ممتاز! جميع الميزات متاحة.';
        } else if (this.criticalIssues.length === 0 && successRate >= 75) {
            status = 'good';
            statusMessage = '✅ النظام v4.0 يعمل بشكل جيد مع بعض التحذيرات البسيطة.';
        } else if (this.criticalIssues.length <= 2) {
            status = 'limited';
            statusMessage = '⚠️ النظام v4.0 يعمل مع قيود - يحتاج إصلاح بعض المشاكل.';
        } else {
            status = 'critical';
            statusMessage = '🚨 النظام v4.0 يحتاج مراجعة شاملة - مشاكل حرجة متعددة.';
        }
        
        console.log(statusMessage);
        
        return {
            status,
            successRate,
            total,
            passed: this.passed,
            failed: this.failed,
            criticalIssues: this.criticalIssues.length,
            warnings: this.warnings.length,
            message: statusMessage,
            details: this.verificationResults
        };
    }
}

// إنشاء مثيل التحقق العام
window.systemVerificationV4 = new SystemVerificationV4();

// تصدير الكلاس
window.SystemVerificationV4 = SystemVerificationV4;

// دالة سريعة للتحقق
window.verifySystemV4 = async () => {
    return await window.systemVerificationV4.performCompleteVerification();
};

console.log('🔍 Bug Bounty System v4.0 Verification تم تحميله بنجاح');
console.log('💡 للتحقق من النظام: verifySystemV4()');
