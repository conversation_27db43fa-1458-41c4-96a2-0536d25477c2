<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل القالب الأصلي - Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار تحميل القالب الأصلي</h1>
            <p>Bug Bounty v4.0 - الحل النهائي لمشكلة التصدير</p>
        </div>

        <div class="test-section">
            <h2>📋 معلومات الاختبار</h2>
            <div class="info">
                <strong>الهدف:</strong> التحقق من أن النظام يحمل القالب من الملف الأصلي بدلاً من القالب المدمج<br>
                <strong>الملف المطلوب:</strong> /assets/modules/bugbounty/report_template.html<br>
                <strong>الطريقة:</strong> استخدام fetch() مع معالجة الأخطاء الشاملة
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 اختبارات التحميل</h2>
            <button class="btn" onclick="testDirectFetch()">اختبار fetch مباشر</button>
            <button class="btn" onclick="testBugBountyCore()">اختبار BugBountyCore</button>
            <button class="btn" onclick="testTemplateProcessing()">اختبار معالجة القالب</button>
            <button class="btn" onclick="clearResults()">مسح النتائج</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 تفاصيل النتائج</h2>
            <div id="detailedResults"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            console.log(message);
        }

        function logDetailed(title, content) {
            const detailedDiv = document.getElementById('detailedResults');
            const section = document.createElement('div');
            section.innerHTML = `
                <h3>${title}</h3>
                <div class="code-block">${content}</div>
            `;
            detailedDiv.appendChild(section);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('detailedResults').innerHTML = '';
        }

        // اختبار fetch مباشر للقالب
        async function testDirectFetch() {
            log('🔍 بدء اختبار fetch مباشر للقالب...', 'info');
            
            try {
                const response = await fetch('/assets/modules/bugbounty/report_template.html');
                log(`📊 استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`فشل تحميل القالب: ${response.status} ${response.statusText}`);
                }
                
                const templateContent = await response.text();
                log(`✅ تم تحميل القالب بنجاح - الحجم: ${templateContent.length} حرف`, 'success');
                
                // فحص محتوى القالب
                const hasHTML = templateContent.includes('<html>') || templateContent.includes('<!DOCTYPE html>');
                const hasTitle = templateContent.includes('تقرير Bug Bounty');
                const hasPlaceholders = templateContent.includes('{{TARGET_URL}}');
                
                log(`📋 فحص المحتوى: HTML=${hasHTML}, Title=${hasTitle}, Placeholders=${hasPlaceholders}`, 'info');
                
                if (templateContent.length > 100 && hasHTML && hasTitle) {
                    log('✅ القالب صالح ويحتوي على المحتوى المطلوب', 'success');
                    logDetailed('محتوى القالب (أول 500 حرف)', templateContent.substring(0, 500) + '...');
                } else {
                    log('❌ القالب غير صالح أو لا يحتوي على المحتوى المطلوب', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في fetch مباشر: ${error.message}`, 'error');
                logDetailed('تفاصيل الخطأ', error.stack || error.toString());
            }
        }

        // اختبار BugBountyCore
        async function testBugBountyCore() {
            log('🔍 بدء اختبار BugBountyCore...', 'info');
            
            try {
                // إنشاء مثيل جديد
                bugBountyCore = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore بنجاح', 'success');
                
                // فحص الحالة الأولية
                log(`📊 حالة القالب الأولية: ${bugBountyCore.reportTemplateHTML ? 'محمل' : 'غير محمل'}`, 'info');
                
                // اختبار دالة loadReportTemplate
                if (typeof bugBountyCore.loadReportTemplate === 'function') {
                    log('✅ دالة loadReportTemplate موجودة', 'success');
                    
                    // تشغيل الدالة
                    await bugBountyCore.loadReportTemplate();
                    
                    // فحص النتيجة
                    if (bugBountyCore.reportTemplateHTML && bugBountyCore.reportTemplateHTML.length > 100) {
                        log(`✅ تم تحميل القالب بنجاح - الحجم: ${bugBountyCore.reportTemplateHTML.length} حرف`, 'success');
                        
                        // فحص محتوى القالب
                        const hasHTML = bugBountyCore.reportTemplateHTML.includes('<html>') || bugBountyCore.reportTemplateHTML.includes('<!DOCTYPE html>');
                        const hasTitle = bugBountyCore.reportTemplateHTML.includes('تقرير Bug Bounty');
                        const hasPlaceholders = bugBountyCore.reportTemplateHTML.includes('{{TARGET_URL}}');
                        
                        log(`📋 فحص المحتوى: HTML=${hasHTML}, Title=${hasTitle}, Placeholders=${hasPlaceholders}`, 'info');
                        
                        if (hasHTML && hasTitle && hasPlaceholders) {
                            log('✅ القالب المحمل صالح ويحتوي على جميع العناصر المطلوبة', 'success');
                            logDetailed('محتوى القالب المحمل (أول 500 حرف)', 
                                bugBountyCore.reportTemplateHTML.substring(0, 500) + '...');
                        } else {
                            log('⚠️ القالب محمل لكن قد يفتقر لبعض العناصر', 'error');
                        }
                        
                    } else {
                        log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                    }
                    
                } else {
                    log('❌ دالة loadReportTemplate غير موجودة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار BugBountyCore: ${error.message}`, 'error');
                logDetailed('تفاصيل الخطأ', error.stack || error.toString());
            }
        }

        // اختبار معالجة القالب
        async function testTemplateProcessing() {
            log('🔍 بدء اختبار معالجة القالب...', 'info');
            
            if (!bugBountyCore) {
                log('⚠️ يجب تشغيل اختبار BugBountyCore أولاً', 'error');
                return;
            }
            
            try {
                if (!bugBountyCore.reportTemplateHTML) {
                    log('🔄 تحميل القالب أولاً...', 'info');
                    await bugBountyCore.loadReportTemplate();
                }
                
                if (bugBountyCore.reportTemplateHTML) {
                    // محاكاة معالجة القالب
                    let processedTemplate = bugBountyCore.reportTemplateHTML;
                    
                    // استبدال المتغيرات
                    processedTemplate = processedTemplate.replace('{{TARGET_URL}}', 'https://example.com');
                    processedTemplate = processedTemplate.replace('{{TIMESTAMP}}', new Date().toLocaleString('ar'));
                    processedTemplate = processedTemplate.replace('{{VULNERABILITIES_CONTENT}}', 'تم اكتشاف 3 ثغرات أمنية');
                    
                    log('✅ تم معالجة القالب بنجاح', 'success');
                    log(`📊 حجم القالب المعالج: ${processedTemplate.length} حرف`, 'info');
                    
                    // فحص أن الاستبدال تم
                    const hasPlaceholders = processedTemplate.includes('{{');
                    const hasTargetUrl = processedTemplate.includes('https://example.com');
                    
                    log(`📋 نتائج المعالجة: متغيرات متبقية=${hasPlaceholders}, URL مستبدل=${hasTargetUrl}`, 'info');
                    
                    if (!hasPlaceholders && hasTargetUrl) {
                        log('✅ معالجة القالب تمت بنجاح - جاهز للتصدير', 'success');
                        logDetailed('القالب المعالج (أول 800 حرف)', 
                            processedTemplate.substring(0, 800) + '...');
                    } else {
                        log('⚠️ قد تكون هناك مشاكل في معالجة القالب', 'error');
                    }
                    
                } else {
                    log('❌ لا يوجد قالب محمل للمعالجة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في معالجة القالب: ${error.message}`, 'error');
                logDetailed('تفاصيل الخطأ', error.stack || error.toString());
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 تم تحميل صفحة الاختبار - جاهز للاختبار', 'info');
            log('💡 اضغط على الأزرار أعلاه لبدء الاختبارات', 'info');
        });
    </script>
</body>
</html>
