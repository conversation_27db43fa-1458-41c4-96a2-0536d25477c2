<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Final Test Now</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Final Test Now</h1>
    <button onclick="runFinalTestNow()">🚀 Run Final Test Now</button>
    
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runFinalTestNow() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 اختبار الإصلاح النهائي الآن...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const core = new BugBountyCore();
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحضير النظام - القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار مع البيانات التي فشلت سابقاً
                const testData = {
                    total_vulnerabilities: 2,
                    vulnerabilities: [
                        { name: 'XSS Test', severity: 'High', description: 'Test XSS vulnerability' },
                        { name: 'SQL Test', severity: 'Critical', description: 'Test SQL injection' }
                    ]
                };
                
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    
                    if (report && report.length > 1000) {
                        log(`🎉 نجح! حجم التقرير: ${report.length} حرف`, 'success');
                        
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length} - ${variables.slice(0, 3).join(', ')}`, 'info');
                        } else {
                            log('✅ تم استبدال جميع المتغيرات!', 'success');
                        }
                        
                        // فحص المحتوى
                        const hasVulnCount = report.includes('2') && !report.includes('{{TOTAL_VULNERABILITIES}}');
                        const hasTargetUrl = report.includes('https://test.com') && !report.includes('{{TARGET_URL}}');
                        const hasTimestamp = !report.includes('{{TIMESTAMP}}');
                        
                        log(`📊 فحص المحتوى:`, 'info');
                        log(`   - عدد الثغرات: ${hasVulnCount ? '✅' : '❌'}`, hasVulnCount ? 'success' : 'error');
                        log(`   - رابط الموقع: ${hasTargetUrl ? '✅' : '❌'}`, hasTargetUrl ? 'success' : 'error');
                        log(`   - الطابع الزمني: ${hasTimestamp ? '✅' : '❌'}`, hasTimestamp ? 'success' : 'error');
                        
                        if (hasVulnCount && hasTargetUrl && hasTimestamp && !variables) {
                            log('🎉🎉🎉 تم إصلاح المشكلة نهائياً! 🎉🎉🎉', 'success');
                            log('✅ النظام جاهز للاستخدام الفعلي', 'success');
                            log('🚀 يمكنك الآن تشغيل Bug Bounty بثقة!', 'success');
                            
                            // إنشاء رابط تحميل
                            const blob = new Blob([report], { type: 'text/html' });
                            const url = URL.createObjectURL(blob);
                            
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = 'success-final-report.html';
                            link.textContent = '🎉 تحميل التقرير الناجح النهائي!';
                            link.style.display = 'block';
                            link.style.margin = '15px 0';
                            link.style.padding = '20px';
                            link.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                            link.style.color = 'white';
                            link.style.textDecoration = 'none';
                            link.style.borderRadius = '10px';
                            link.style.fontSize = '18px';
                            link.style.fontWeight = 'bold';
                            link.style.textAlign = 'center';
                            link.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                            
                            document.getElementById('results').appendChild(link);
                            
                        } else {
                            log('⚠️ بعض الفحوصات فشلت - يحتاج مراجعة', 'info');
                        }
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ: ${reportError.message}`, 'error');
                    
                    if (reportError.message.includes('forEach')) {
                        log('❌ ما زالت هناك مشكلة forEach - يحتاج إصلاح إضافي', 'error');
                    } else {
                        log('ℹ️ نوع خطأ مختلف - قد يكون تقدم!', 'info');
                    }
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
