/**
 * Diagnostic Logger - ملف التشخيص المنفصل
 * يسجل جميع العمليات والأخطاء لتسهيل التشخيص
 */

class DiagnosticLogger {
    constructor() {
        this.logs = [];
        this.startTime = new Date();
        this.logContainer = null;
        this.createLogContainer();
    }

    createLogContainer() {
        // إنشاء نافذة التشخيص
        const container = document.createElement('div');
        container.id = 'diagnostic-logger';
        container.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 500px;
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 2px solid #00ff00;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            z-index: 10000;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        `;

        const header = document.createElement('div');
        header.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>🔍 Bug Bounty Diagnostic Logger</strong>
                <button onclick="this.parentElement.parentElement.parentElement.style.display='none'" 
                        style="background: #ff0000; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">✕</button>
            </div>
        `;

        const logArea = document.createElement('div');
        logArea.id = 'diagnostic-logs';
        logArea.style.cssText = `
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 5px;
            background: #000;
        `;

        container.appendChild(header);
        container.appendChild(logArea);
        document.body.appendChild(container);
        
        this.logContainer = logArea;
    }

    log(level, category, message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            level,
            category,
            message,
            data,
            elapsed: Date.now() - this.startTime.getTime()
        };

        this.logs.push(logEntry);
        this.displayLog(logEntry);
        
        // تسجيل في وحدة التحكم أيضاً
        const consoleMessage = `[${timestamp}] [${level}] [${category}] ${message}`;
        switch (level) {
            case 'ERROR':
                console.error(consoleMessage, data);
                break;
            case 'WARN':
                console.warn(consoleMessage, data);
                break;
            case 'INFO':
                console.info(consoleMessage, data);
                break;
            default:
                console.log(consoleMessage, data);
        }
    }

    displayLog(logEntry) {
        if (!this.logContainer) return;

        const logDiv = document.createElement('div');
        logDiv.style.cssText = `
            margin: 2px 0;
            padding: 2px;
            border-left: 3px solid ${this.getLevelColor(logEntry.level)};
            padding-left: 5px;
        `;

        const dataStr = logEntry.data ? ` | Data: ${JSON.stringify(logEntry.data).substring(0, 100)}...` : '';
        logDiv.innerHTML = `
            <span style="color: #888;">[${logEntry.timestamp}]</span>
            <span style="color: ${this.getLevelColor(logEntry.level)};">[${logEntry.level}]</span>
            <span style="color: #ffff00;">[${logEntry.category}]</span>
            <span style="color: #ffffff;">${logEntry.message}</span>
            <span style="color: #888; font-size: 10px;">${dataStr}</span>
        `;

        this.logContainer.appendChild(logDiv);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;

        // الحد من عدد السجلات المعروضة
        if (this.logContainer.children.length > 100) {
            this.logContainer.removeChild(this.logContainer.firstChild);
        }
    }

    getLevelColor(level) {
        switch (level) {
            case 'ERROR': return '#ff0000';
            case 'WARN': return '#ffaa00';
            case 'INFO': return '#00aaff';
            case 'SUCCESS': return '#00ff00';
            default: return '#ffffff';
        }
    }

    error(category, message, data) {
        this.log('ERROR', category, message, data);
    }

    warn(category, message, data) {
        this.log('WARN', category, message, data);
    }

    info(category, message, data) {
        this.log('INFO', category, message, data);
    }

    success(category, message, data) {
        this.log('SUCCESS', category, message, data);
    }

    // تصدير السجلات
    exportLogs() {
        const logsText = this.logs.map(log => 
            `[${log.timestamp}] [${log.level}] [${log.category}] ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
        ).join('\n');

        const blob = new Blob([logsText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bug-bounty-diagnostic-${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // مسح السجلات
    clearLogs() {
        this.logs = [];
        if (this.logContainer) {
            this.logContainer.innerHTML = '';
        }
    }

    // إحصائيات السجلات
    getStats() {
        const stats = {
            total: this.logs.length,
            errors: this.logs.filter(log => log.level === 'ERROR').length,
            warnings: this.logs.filter(log => log.level === 'WARN').length,
            info: this.logs.filter(log => log.level === 'INFO').length,
            success: this.logs.filter(log => log.level === 'SUCCESS').length
        };
        return stats;
    }
}

// إنشاء مثيل عام للتشخيص
window.diagnosticLogger = new DiagnosticLogger();

// إضافة أزرار التحكم
setTimeout(() => {
    const controlPanel = document.createElement('div');
    controlPanel.style.cssText = `
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: #333;
        padding: 10px;
        border-radius: 5px;
        z-index: 10001;
    `;
    
    controlPanel.innerHTML = `
        <button onclick="diagnosticLogger.exportLogs()" style="margin: 2px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">تصدير السجلات</button>
        <button onclick="diagnosticLogger.clearLogs()" style="margin: 2px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">مسح السجلات</button>
        <button onclick="console.log(diagnosticLogger.getStats())" style="margin: 2px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">إحصائيات</button>
    `;
    
    document.body.appendChild(controlPanel);
}, 1000);

console.log('🔍 Diagnostic Logger initialized successfully');
