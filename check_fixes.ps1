# Check Bug Bounty v4.0 fixes
Write-Host "Checking Bug Bounty v4.0 fixes..." -ForegroundColor Cyan

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "ERROR: BugBountyCore.js not found" -ForegroundColor Red
    exit 1
}

$content = Get-Content $bugBountyFile -Raw

# Check for generic texts that should be fixed
Write-Host "`nChecking for generic texts:" -ForegroundColor Yellow

$payloadExampleCount = ([regex]::Matches($content, "payload_example")).Count
Write-Host "payload_example found: $payloadExampleCount times" -ForegroundColor $(if($payloadExampleCount -eq 0) {"Green"} else {"Red"})

# Check for new functions
Write-Host "`nChecking for new functions:" -ForegroundColor Yellow

$newFunctions = @(
    "generateRealPayload",
    "generateRealExploitationResult", 
    "generateRealEvidence",
    "generateRealResponse",
    "generateRealRequest",
    "generateRealParameter"
)

$addedFunctions = 0
foreach ($func in $newFunctions) {
    if ($content -match "$func\s*\(") {
        Write-Host "ADDED: $func" -ForegroundColor Green
        $addedFunctions++
    } else {
        Write-Host "MISSING: $func" -ForegroundColor Red
    }
}

# Check function usage
Write-Host "`nChecking function usage:" -ForegroundColor Yellow

$generateRealPayloadUsage = ([regex]::Matches($content, "this\.generateRealPayload\(")).Count
Write-Host "generateRealPayload used: $generateRealPayloadUsage times" -ForegroundColor $(if($generateRealPayloadUsage -gt 0) {"Green"} else {"Red"})

$generateRealEvidenceUsage = ([regex]::Matches($content, "this\.generateRealEvidence\(")).Count
Write-Host "generateRealEvidence used: $generateRealEvidenceUsage times" -ForegroundColor $(if($generateRealEvidenceUsage -gt 0) {"Green"} else {"Red"})

$generateRealResponseUsage = ([regex]::Matches($content, "this\.generateRealResponse\(")).Count
Write-Host "generateRealResponse used: $generateRealResponseUsage times" -ForegroundColor $(if($generateRealResponseUsage -gt 0) {"Green"} else {"Red"})

# Check specific improvements
Write-Host "`nChecking specific improvements:" -ForegroundColor Yellow

if ($content -match "تم استخراج.*سجل مستخدم") {
    Write-Host "FOUND: Real exploitation results" -ForegroundColor Green
} else {
    Write-Host "MISSING: Real exploitation results" -ForegroundColor Red
}

if ($content -match "تم التقاط صور") {
    Write-Host "FOUND: Real evidence descriptions" -ForegroundColor Green
} else {
    Write-Host "MISSING: Real evidence descriptions" -ForegroundColor Red
}

if ($content -match "HTTP 200 OK - تم إرجاع") {
    Write-Host "FOUND: Real server responses" -ForegroundColor Green
} else {
    Write-Host "MISSING: Real server responses" -ForegroundColor Red
}

# Final results
Write-Host "`nFinal Results:" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

$totalScore = 0

# Score for removing generic texts
if ($payloadExampleCount -eq 0) { 
    $totalScore += 30
    Write-Host "EXCELLENT: payload_example completely removed" -ForegroundColor Green
} else {
    Write-Host "ISSUE: payload_example still found $payloadExampleCount times" -ForegroundColor Red
}

# Score for adding functions
$totalScore += ($addedFunctions * 10)
Write-Host "Functions added: $addedFunctions of $($newFunctions.Count) (Score: $($addedFunctions * 10))" -ForegroundColor $(if($addedFunctions -eq $newFunctions.Count) {"Green"} else {"Yellow"})

# Score for function usage
$usageScore = 0
if ($generateRealPayloadUsage -gt 0) { $usageScore += 5 }
if ($generateRealEvidenceUsage -gt 0) { $usageScore += 5 }
if ($generateRealResponseUsage -gt 0) { $usageScore += 5 }
$totalScore += $usageScore

Write-Host "Function usage score: $usageScore/15" -ForegroundColor $(if($usageScore -eq 15) {"Green"} else {"Yellow"})

Write-Host "`nTotal Score: $totalScore/100" -ForegroundColor $(if($totalScore -ge 90) {"Green"} elseif($totalScore -ge 70) {"Yellow"} else {"Red"})

if ($totalScore -ge 90) {
    Write-Host "`nEXCELLENT: System successfully fixed and ready for production!" -ForegroundColor Green
    Write-Host "The Bug Bounty system now generates real vulnerability-specific content" -ForegroundColor Green
} elseif ($totalScore -ge 70) {
    Write-Host "`nGOOD: Most fixes completed, minor improvements needed" -ForegroundColor Yellow
} else {
    Write-Host "`nNEEDS WORK: More fixes required to eliminate generic content" -ForegroundColor Red
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Open test_real_vulnerability_report.html to see the improvements" -ForegroundColor White
Write-Host "2. Test the actual Bug Bounty system at localhost:3000" -ForegroundColor White
Write-Host "3. Generate a real vulnerability report to verify the fixes" -ForegroundColor White
