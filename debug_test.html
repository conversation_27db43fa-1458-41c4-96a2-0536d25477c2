<!DOCTYPE html>
<html>
<head>
    <title>Debug Test - Template Loading</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔍 Debug Test - Template Loading</h1>
    <div id="output" class="log"></div>
    
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type;
            output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function debugTest() {
            try {
                log('🔄 بدء اختبار التشخيص...', 'info');
                
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // التحقق من القالب المحفوظ
                if (core.cachedTemplate) {
                    log(`✅ القالب محفوظ في الذاكرة: ${core.cachedTemplate.length} حرف`, 'success');
                } else {
                    log('⚠️ لا يوجد قالب محفوظ في الذاكرة', 'info');
                }
                
                // محاولة تحميل القالب
                await core.preloadTemplate();
                
                if (core.cachedTemplate) {
                    log(`✅ القالب متاح الآن: ${core.cachedTemplate.length} حرف`, 'success');
                    log(`📋 بداية القالب: ${core.cachedTemplate.substring(0, 100)}...`, 'info');
                } else {
                    log('❌ فشل في تحميل القالب', 'error');
                    return;
                }
                
                // اختبار دالة generateFinalComprehensiveReport مع تشخيص مفصل
                log('🔄 اختبار generateFinalComprehensiveReport...', 'info');
                
                const mockAnalysis = {
                    vulnerabilities: [],
                    total_vulnerabilities: 0,
                    pages_processed: 1,
                    scan_timestamp: new Date().toISOString()
                };
                
                // إضافة console.log مؤقت لتتبع التنفيذ
                const originalConsoleLog = console.log;
                const originalConsoleError = console.error;
                
                console.log = function(...args) {
                    log(`[CONSOLE] ${args.join(' ')}`, 'info');
                    originalConsoleLog.apply(console, args);
                };
                
                console.error = function(...args) {
                    log(`[ERROR] ${args.join(' ')}`, 'error');
                    originalConsoleError.apply(console, args);
                };
                
                try {
                    const report = await core.generateFinalComprehensiveReport(mockAnalysis, [], 'https://example.com');
                    
                    if (report) {
                        log(`✅ تم إنشاء التقرير بنجاح: ${report.length} حرف`, 'success');
                    } else {
                        log('❌ التقرير فارغ', 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في generateFinalComprehensiveReport: ${error.message}`, 'error');
                    log(`📋 Stack trace: ${error.stack}`, 'error');
                } finally {
                    // استعادة console.log الأصلي
                    console.log = originalConsoleLog;
                    console.error = originalConsoleError;
                }
                
                log('🏁 انتهى اختبار التشخيص', 'info');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                log(`📋 Stack trace: ${error.stack}`, 'error');
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(debugTest, 1000);
        });
    </script>
</body>
</html>
