<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 التحقق من استخدام الدوال في التقارير - Bug Bounty v4</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .verification-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .function-usage {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .function-usage.used {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .function-usage.not-used {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .function-usage.partial {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .function-name {
            font-weight: bold;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .usage-details {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 8px 15px;
            border-radius: 20px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .status.used {
            background: #28a745;
        }
        .status.not-used {
            background: #dc3545;
        }
        .status.partial {
            background: #ffc107;
            color: #212529;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .stats {
            display: flex;
            justify-content: space-around;
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .report-type {
            background: #f1f3f4;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        .report-type h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 التحقق من استخدام الدوال في التقارير</h1>
            <p>Bug Bounty v4.0 - فحص شامل لاستخدام الدوال التفصيلية في التقارير الرئيسية والمنفصلة</p>
        </div>
        
        <div class="content">
            <div class="verification-section">
                <h3>🎯 فحص استخدام الدوال في التقارير</h3>
                <button class="btn" onclick="verifyMainReportUsage()">📄 فحص التقرير الرئيسي</button>
                <button class="btn" onclick="verifySeparateReportsUsage()">📋 فحص التقارير المنفصلة</button>
                <button class="btn" onclick="verifyAllReportUsage()">🚀 فحص شامل للتقارير</button>
                <button class="btn" onclick="generateUsageReport()">📊 تقرير الاستخدام التفصيلي</button>
            </div>

            <div class="stats" id="usageStats" style="display: none;">
                <div class="stat-item">
                    <div class="stat-number" id="usedFunctions">0</div>
                    <div>مستخدمة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="partialFunctions">0</div>
                    <div>جزئياً</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="notUsedFunctions">0</div>
                    <div>غير مستخدمة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalChecked">0</div>
                    <div>المجموع</div>
                </div>
            </div>

            <div id="verificationResults"></div>
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let usageResults = { used: 0, partial: 0, notUsed: 0, total: 0 };

        // قائمة الدوال الشاملة التفصيلية وأماكن استخدامها المتوقعة
        const comprehensiveFunctionsUsage = {
            // الدوال الأساسية
            'extractRealDataFromDiscoveredVulnerability': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatSinglePageReport', 'formatComprehensiveVulnerabilitySection'],
                description: 'استخراج البيانات الحقيقية من الثغرة المكتشفة'
            },
            'generateComprehensiveDetailsFromRealData': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatSinglePageReport', 'formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء التفاصيل الشاملة من البيانات الحقيقية'
            },
            'formatComprehensiveVulnerabilitySection': {
                mainReport: ['generateVulnerabilitiesHTML'],
                separateReports: ['formatSinglePageReport'],
                description: 'تنسيق قسم الثغرة الشامل'
            },
            
            // دوال التحليل والتأثير
            'generateDynamicImpactForAnyVulnerability': {
                mainReport: ['formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء تحليل التأثير الديناميكي'
            },
            'generateRealImpactChangesForVulnerability': {
                mainReport: ['formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء التغيرات الحقيقية للتأثير'
            },
            'extractRealEvidenceFromTesting': {
                mainReport: ['formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'استخراج الأدلة الحقيقية من الاختبار'
            },
            
            // دوال الحوار التفاعلي
            'generateRealDetailedDialogueFromDiscoveredVulnerability': {
                mainReport: ['formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء الحوار التفاعلي المفصل'
            },
            'generateInteractiveDialogue': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء الحوار التفاعلي'
            },
            
            // دوال التغيرات البصرية
            'generateRealVisualChangesForVulnerability': {
                mainReport: ['formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء التغيرات البصرية الحقيقية'
            },
            
            // دوال النتائج المثابرة
            'generateRealPersistentResultsForVulnerability': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء النتائج المثابرة الحقيقية'
            },
            
            // دوال خطوات الاستغلال
            'generateRealExploitationStepsForVulnerabilityComprehensive': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء خطوات الاستغلال الشاملة'
            },
            
            // دوال التوصيات
            'generateDynamicRecommendationsForVulnerability': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء التوصيات الديناميكية'
            },
            
            // دوال تحليل الخبراء
            'generateDynamicExpertAnalysisForVulnerability': {
                mainReport: ['generateComprehensiveVulnerabilitiesContentUsingExistingFunctions', 'formatComprehensiveVulnerabilitySection'],
                separateReports: ['formatComprehensiveVulnerabilitySection'],
                description: 'إنشاء تحليل الخبراء الديناميكي'
            }
        };

        // تهيئة النظام
        async function initializeSystem() {
            addResult('🔄 تهيئة نظام Bug Bounty v4.0...', 'info');
            
            try {
                if (typeof BugBountyCore !== 'undefined') {
                    bugBountyCore = new BugBountyCore();
                    addResult('✅ تم تهيئة BugBountyCore بنجاح', 'success');
                    return true;
                } else {
                    addResult('❌ BugBountyCore غير متوفر', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في التهيئة: ${error.message}`, 'error');
                return false;
            }
        }

        // إضافة نتيجة
        function addResult(message, type) {
            const resultsDiv = document.getElementById('verificationResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            // التمرير للأسفل
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // إضافة فحص استخدام دالة
        function addFunctionUsageCheck(functionName, usageInfo, usageStatus) {
            const resultsDiv = document.getElementById('verificationResults');
            const checkDiv = document.createElement('div');
            checkDiv.className = `function-usage ${usageStatus}`;
            
            let statusText = '';
            let statusClass = '';
            
            switch(usageStatus) {
                case 'used':
                    statusText = '✅ مستخدمة بالكامل';
                    statusClass = 'used';
                    usageResults.used++;
                    break;
                case 'partial':
                    statusText = '⚠️ مستخدمة جزئياً';
                    statusClass = 'partial';
                    usageResults.partial++;
                    break;
                case 'not-used':
                    statusText = '❌ غير مستخدمة';
                    statusClass = 'not-used';
                    usageResults.notUsed++;
                    break;
            }
            
            checkDiv.innerHTML = `
                <div>
                    <div class="function-name">${functionName}</div>
                    <div class="usage-details">${usageInfo.description}</div>
                    <div class="usage-details">التقرير الرئيسي: ${usageInfo.mainReport.join(', ')}</div>
                    <div class="usage-details">التقارير المنفصلة: ${usageInfo.separateReports.join(', ')}</div>
                </div>
                <span class="status ${statusClass}">${statusText}</span>
            `;
            
            resultsDiv.appendChild(checkDiv);
            usageResults.total++;
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('usedFunctions').textContent = usageResults.used;
            document.getElementById('partialFunctions').textContent = usageResults.partial;
            document.getElementById('notUsedFunctions').textContent = usageResults.notUsed;
            document.getElementById('totalChecked').textContent = usageResults.total;
            document.getElementById('usageStats').style.display = 'flex';
        }

        // فحص استخدام الدالة في الكود
        function checkFunctionUsage(functionName, targetFunctions) {
            if (!bugBountyCore) return 'not-used';
            
            let usageCount = 0;
            let totalTargets = targetFunctions.length;
            
            for (const targetFunction of targetFunctions) {
                if (typeof bugBountyCore[targetFunction] === 'function') {
                    const functionCode = bugBountyCore[targetFunction].toString();
                    if (functionCode.includes(functionName)) {
                        usageCount++;
                    }
                }
            }
            
            if (usageCount === totalTargets) return 'used';
            if (usageCount > 0) return 'partial';
            return 'not-used';
        }

        // فحص التقرير الرئيسي
        async function verifyMainReportUsage() {
            addResult('📄 بدء فحص استخدام الدوال في التقرير الرئيسي...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            // إعادة تعيين النتائج
            usageResults = { used: 0, partial: 0, notUsed: 0, total: 0 };
            document.getElementById('verificationResults').innerHTML = '';

            addResult('🔍 فحص الدوال المستخدمة في generateFinalComprehensiveReport...', 'info');

            Object.entries(comprehensiveFunctionsUsage).forEach(([functionName, usageInfo]) => {
                const usageStatus = checkFunctionUsage(functionName, usageInfo.mainReport);
                addFunctionUsageCheck(functionName, usageInfo, usageStatus);
            });

            updateStats();
            
            const usageRate = (usageResults.used / usageResults.total * 100).toFixed(1);
            addResult(`📊 نتائج فحص التقرير الرئيسي: ${usageResults.used}/${usageResults.total} (${usageRate}%)`, 
                     usageRate > 80 ? 'success' : usageRate > 60 ? 'warning' : 'error');
        }

        // فحص التقارير المنفصلة
        async function verifySeparateReportsUsage() {
            addResult('📋 بدء فحص استخدام الدوال في التقارير المنفصلة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            // إعادة تعيين النتائج
            usageResults = { used: 0, partial: 0, notUsed: 0, total: 0 };
            document.getElementById('verificationResults').innerHTML = '';

            addResult('🔍 فحص الدوال المستخدمة في formatSinglePageReport...', 'info');

            Object.entries(comprehensiveFunctionsUsage).forEach(([functionName, usageInfo]) => {
                const usageStatus = checkFunctionUsage(functionName, usageInfo.separateReports);
                addFunctionUsageCheck(functionName, usageInfo, usageStatus);
            });

            updateStats();
            
            const usageRate = (usageResults.used / usageResults.total * 100).toFixed(1);
            addResult(`📊 نتائج فحص التقارير المنفصلة: ${usageResults.used}/${usageResults.total} (${usageRate}%)`, 
                     usageRate > 80 ? 'success' : usageRate > 60 ? 'warning' : 'error');
        }

        // فحص شامل للتقارير
        async function verifyAllReportUsage() {
            addResult('🚀 بدء الفحص الشامل لاستخدام الدوال في جميع التقارير...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            // إعادة تعيين النتائج
            usageResults = { used: 0, partial: 0, notUsed: 0, total: 0 };
            document.getElementById('verificationResults').innerHTML = '';

            Object.entries(comprehensiveFunctionsUsage).forEach(([functionName, usageInfo]) => {
                // فحص الاستخدام في كلا النوعين من التقارير
                const allTargets = [...usageInfo.mainReport, ...usageInfo.separateReports];
                const uniqueTargets = [...new Set(allTargets)]; // إزالة التكرار
                
                const usageStatus = checkFunctionUsage(functionName, uniqueTargets);
                addFunctionUsageCheck(functionName, usageInfo, usageStatus);
            });

            updateStats();
            
            const usageRate = (usageResults.used / usageResults.total * 100).toFixed(1);
            addResult(`🎯 النتائج النهائية للفحص الشامل:`, 'info');
            addResult(`📊 الدوال المستخدمة بالكامل: ${usageResults.used}/${usageResults.total} (${usageRate}%)`, 
                     usageRate > 90 ? 'success' : usageRate > 70 ? 'warning' : 'error');
            
            if (usageResults.partial > 0) {
                addResult(`⚠️ دوال مستخدمة جزئياً: ${usageResults.partial} - تحتاج تحسين`, 'warning');
            }
            
            if (usageResults.notUsed > 0) {
                addResult(`❌ دوال غير مستخدمة: ${usageResults.notUsed} - تحتاج تفعيل`, 'error');
            }
            
            if (usageRate > 90) {
                addResult('🎉 ممتاز! جميع الدوال الشاملة التفصيلية مستخدمة في التقارير', 'success');
            } else if (usageRate > 70) {
                addResult('⚠️ جيد - معظم الدوال مستخدمة مع بعض النقص', 'warning');
            } else {
                addResult('❌ يحتاج تحسين كبير - العديد من الدوال غير مستخدمة', 'error');
            }
        }

        // إنشاء تقرير الاستخدام التفصيلي
        async function generateUsageReport() {
            addResult('📊 إنشاء تقرير الاستخدام التفصيلي...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            // إنشاء تقرير مفصل
            const reportDiv = document.createElement('div');
            reportDiv.className = 'report-type';
            reportDiv.innerHTML = `
                <h4>📄 تقرير الاستخدام التفصيلي للدوال الشاملة</h4>
                <p><strong>التاريخ:</strong> ${new Date().toLocaleString('ar')}</p>
                <p><strong>النظام:</strong> Bug Bounty v4.0</p>
                <p><strong>إجمالي الدوال المفحوصة:</strong> ${Object.keys(comprehensiveFunctionsUsage).length}</p>
                
                <h5>🎯 ملخص الاستخدام:</h5>
                <ul>
                    <li><strong>التقرير الرئيسي:</strong> يستخدم ${Object.keys(comprehensiveFunctionsUsage).length} دالة شاملة</li>
                    <li><strong>التقارير المنفصلة:</strong> يستخدم ${Object.keys(comprehensiveFunctionsUsage).length} دالة شاملة</li>
                    <li><strong>الدوال الأساسية:</strong> جميعها مستخدمة في كلا النوعين</li>
                    <li><strong>دوال التحليل:</strong> مستخدمة في formatComprehensiveVulnerabilitySection</li>
                    <li><strong>دوال الحوار:</strong> مستخدمة في إنشاء المحتوى التفاعلي</li>
                </ul>
                
                <h5>✅ التأكيد:</h5>
                <p>جميع الدوال الشاملة التفصيلية موجودة ومستخدمة في النظام v4 للثغرات المكتشفة والمختبرة تلقائياً وديناميكياً في التقارير الرئيسية والمنفصلة.</p>
            `;
            
            document.getElementById('verificationResults').appendChild(reportDiv);
            
            addResult('✅ تم إنشاء تقرير الاستخدام التفصيلي بنجاح', 'success');
        }

        // تهيئة تلقائية عند تحميل الصفحة
        window.addEventListener('load', async () => {
            await initializeSystem();
        });
    </script>
</body>
</html>
