<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 اختبار الدوال الشاملة التفصيلية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .console {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 25px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            border: 2px solid #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
        .status {
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }
        .status.warning {
            background: rgba(255, 152, 0, 0.2);
            border: 2px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار الدوال الشاملة التفصيلية</h1>
            <h2>🚀 Bug Bounty v4.0 Comprehensive Functions Test</h2>
            <p>اختبار شامل للدوال التفصيلية الديناميكية في النظام</p>
        </div>

        <div id="status" class="status" style="display: none;">
            جاري التحضير...
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="runComprehensiveTest()">
                🔍 تشغيل اختبار الدوال الشاملة
            </button>
            <button class="btn" onclick="clearConsole()">
                🧹 مسح الشاشة
            </button>
        </div>

        <div class="console" id="console">
🔍 اختبار الدوال الشاملة التفصيلية
جاري تحضير النظام للاختبار...
═══════════════════════════════════════
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        // إعادة توجيه console.log إلى الشاشة
        const originalLog = console.log;
        const consoleElement = document.getElementById('console');
        const statusElement = document.getElementById('status');
        
        console.log = function(...args) {
            // عرض في الشاشة
            const message = args.join(' ');
            consoleElement.textContent += message + '\n';
            consoleElement.scrollTop = consoleElement.scrollHeight;
            
            // عرض في console الأصلي أيضاً
            originalLog.apply(console, args);
        };

        // تحميل وتشغيل اختبار الدوال الشاملة
        async function runComprehensiveTest() {
            try {
                // مسح الشاشة
                clearConsole();
                
                // عرض حالة التحميل
                statusElement.style.display = 'block';
                statusElement.className = 'status warning';
                statusElement.textContent = '⏳ جاري تحميل وتشغيل اختبار الدوال الشاملة...';
                
                // تحميل وتشغيل ملف الاختبار
                const response = await fetch('./test_comprehensive_functions.js');
                const testCode = await response.text();
                
                // تنفيذ الكود
                eval(testCode);
                
                // تشغيل الاختبار
                if (typeof testComprehensiveFunctions === 'function') {
                    statusElement.textContent = '🧪 جاري تشغيل اختبار الدوال الشاملة...';
                    await testComprehensiveFunctions();
                    
                    // فحص النتائج
                    setTimeout(() => {
                        const consoleText = consoleElement.textContent;
                        if (consoleText.includes('جميع الدوال الشاملة التفصيلية تعمل بنجاح')) {
                            statusElement.className = 'status success';
                            statusElement.textContent = '✅ جميع الدوال الشاملة التفصيلية تعمل بنجاح!';
                        } else if (consoleText.includes('بعض الدوال الشاملة التفصيلية لا تعمل')) {
                            statusElement.className = 'status warning';
                            statusElement.textContent = '⚠️ بعض الدوال تحتاج تحسين - راجع التفاصيل أدناه';
                        } else if (consoleText.includes('خطأ')) {
                            statusElement.className = 'status error';
                            statusElement.textContent = '❌ حدث خطأ في الاختبار - راجع التفاصيل أدناه';
                        } else {
                            statusElement.className = 'status success';
                            statusElement.textContent = '✅ اكتمل الاختبار - راجع النتائج أدناه';
                        }
                    }, 2000);
                } else {
                    console.log('❌ خطأ: لم يتم العثور على دالة testComprehensiveFunctions');
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ خطأ في تحميل دالة الاختبار';
                }
                
            } catch (error) {
                console.log(`❌ خطأ في تحميل أو تشغيل الاختبار: ${error.message}`);
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في تحميل الاختبار';
            }
        }

        function clearConsole() {
            consoleElement.textContent = `🔍 اختبار الدوال الشاملة التفصيلية
جاري تحضير النظام للاختبار...
═══════════════════════════════════════
`;
            statusElement.style.display = 'none';
        }

        // تحميل النظام عند بدء الصفحة
        window.addEventListener('load', () => {
            console.log('🚀 النظام جاهز لاختبار الدوال الشاملة التفصيلية!');
            console.log('اضغط على زر "تشغيل اختبار الدوال الشاملة" لبدء الاختبار');
        });
    </script>
</body>
</html>
