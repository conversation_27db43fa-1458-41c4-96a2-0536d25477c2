<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ultimate Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); }
        .log { margin: 8px 0; padding: 10px; border-radius: 8px; border-left: 4px solid; }
        .success { background: rgba(40, 167, 69, 0.2); border-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-color: #dc3545; }
        .info { background: rgba(23, 162, 184, 0.2); border-color: #17a2b8; }
        .warning { background: rgba(255, 193, 7, 0.2); border-color: #ffc107; }
        .test-btn { 
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 25px; 
            cursor: pointer; 
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        .test-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.3); }
        .success-banner { 
            background: linear-gradient(45deg, #00b894, #00cec9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px;">🚀 Ultimate Bug Bounty Test 🚀</h1>
        <div style="text-align: center; margin-bottom: 30px;">
            <button class="test-btn" onclick="runUltimateTest()">🔥 Run Ultimate Test 🔥</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runUltimateTest() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 بدء الاختبار النهائي الشامل...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const core = new BugBountyCore();
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحضير النظام - القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار مع بيانات متنوعة
                const testCases = [
                    {
                        name: 'بيانات صحيحة',
                        data: {
                            vulnerabilities: [
                                { name: 'XSS Test', severity: 'High', description: 'Test XSS vulnerability' },
                                { name: 'SQL Test', severity: 'Critical', description: 'Test SQL injection' }
                            ]
                        }
                    },
                    {
                        name: 'بيانات خاطئة (كما في الاختبارات السابقة)',
                        data: {
                            total_vulnerabilities: 1,
                            vulnerabilities: [
                                { name: 'Test XSS', severity: 'High' }
                            ]
                        }
                    },
                    {
                        name: 'بيانات فارغة',
                        data: {}
                    },
                    {
                        name: 'vulnerabilities غير مصفوفة',
                        data: {
                            vulnerabilities: "not an array"
                        }
                    }
                ];
                
                let allTestsPassed = true;
                
                for (let i = 0; i < testCases.length; i++) {
                    const testCase = testCases[i];
                    log(`🧪 اختبار ${i + 1}: ${testCase.name}...`, 'info');
                    
                    try {
                        const report = await core.generateFinalComprehensiveReport(testCase.data, [], 'https://test.com');
                        
                        if (report && report.length > 1000) {
                            log(`✅ نجح الاختبار ${i + 1}! الحجم: ${report.length} حرف`, 'success');
                            
                            const variables = report.match(/{{[^}]+}}/g);
                            if (variables) {
                                log(`⚠️ متغيرات متبقية: ${variables.length}`, 'warning');
                            } else {
                                log(`✅ تم استبدال جميع المتغيرات في الاختبار ${i + 1}`, 'success');
                            }
                        } else {
                            log(`❌ فشل الاختبار ${i + 1}: التقرير فارغ`, 'error');
                            allTestsPassed = false;
                        }
                        
                    } catch (testError) {
                        log(`❌ فشل الاختبار ${i + 1}: ${testError.message}`, 'error');
                        allTestsPassed = false;
                        
                        if (testError.message.includes('forEach')) {
                            log(`❌ مشكلة forEach في الاختبار ${i + 1}`, 'error');
                        }
                    }
                }
                
                // النتيجة النهائية
                if (allTestsPassed) {
                    const successBanner = document.createElement('div');
                    successBanner.className = 'success-banner';
                    successBanner.innerHTML = `
                        🎉🎉🎉 تم إصلاح جميع المشاكل نهائياً! 🎉🎉🎉<br>
                        ✅ جميع الاختبارات نجحت<br>
                        🚀 النظام جاهز للاستخدام الفعلي<br>
                        🔥 يمكنك الآن تشغيل Bug Bounty بثقة تامة!
                    `;
                    document.getElementById('results').appendChild(successBanner);
                    
                    // إنشاء رابط تحميل للتقرير النهائي
                    const finalTestData = {
                        vulnerabilities: [
                            { name: 'Final XSS Test', severity: 'High', description: 'Final test XSS vulnerability' },
                            { name: 'Final SQL Test', severity: 'Critical', description: 'Final test SQL injection' },
                            { name: 'Final CSRF Test', severity: 'Medium', description: 'Final test CSRF vulnerability' }
                        ]
                    };
                    
                    const finalReport = await core.generateFinalComprehensiveReport(finalTestData, [], 'https://final-test.com');
                    
                    const blob = new Blob([finalReport], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'ultimate-success-report.html';
                    link.textContent = '🎉 تحميل التقرير النهائي الناجح!';
                    link.style.display = 'block';
                    link.style.margin = '20px auto';
                    link.style.padding = '20px';
                    link.style.background = 'linear-gradient(45deg, #00b894, #00cec9)';
                    link.style.color = 'white';
                    link.style.textDecoration = 'none';
                    link.style.borderRadius = '15px';
                    link.style.fontSize = '18px';
                    link.style.fontWeight = 'bold';
                    link.style.textAlign = 'center';
                    link.style.boxShadow = '0 4px 15px rgba(0,0,0,0.3)';
                    link.style.width = '300px';
                    
                    document.getElementById('results').appendChild(link);
                    
                } else {
                    log('❌ بعض الاختبارات فشلت - يحتاج مراجعة إضافية', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
