Write-Host "🔍 البحث عن خطأ vuln is not defined..." -ForegroundColor Yellow

$content = Get-Content 'assets\modules\bugbounty\BugBountyCore.js'
$impactCalls = $content | Select-String -Pattern 'generateImpactFromTechnique'

Write-Host "عدد استخدامات generateImpactFromTechnique: $($impactCalls.Count)" -ForegroundColor Cyan

Write-Host "`n📋 جميع الاستخدامات:" -ForegroundColor Green
foreach ($call in $impactCalls) {
    Write-Host "السطر $($call.LineNumber): $($call.Line.Trim())"
}

Write-Host "`n🔍 البحث عن أخطاء محتملة..." -ForegroundColor Yellow
$errorFound = $false

# البحث عن استخدام خاطئ لمتغير vuln
$vulnErrors = $content | Select-String -Pattern 'generateImpactFromTechnique\(vuln\)'
if ($vulnErrors) {
    Write-Host "❌ وجد خطأ: استخدام vuln بدلاً من technique" -ForegroundColor Red
    foreach ($error in $vulnErrors) {
        Write-Host "السطر $($error.LineNumber): $($error.Line.Trim())"
    }
    $errorFound = $true
}

# البحث عن متغيرات غير معرفة
$undefinedVars = $content | Select-String -Pattern 'vuln is not defined'
if ($undefinedVars) {
    Write-Host "❌ وجد خطأ: vuln is not defined" -ForegroundColor Red
    foreach ($error in $undefinedVars) {
        Write-Host "السطر $($error.LineNumber): $($error.Line.Trim())"
    }
    $errorFound = $true
}

if (-not $errorFound) {
    Write-Host "✅ لم يتم العثور على أخطاء واضحة في الكود" -ForegroundColor Green
}

Write-Host "`n✅ انتهى الفحص" -ForegroundColor Green
