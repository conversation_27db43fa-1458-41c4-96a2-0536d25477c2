<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Template Replacement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .code { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Test Template Replacement</h1>
    <button onclick="testTemplateReplacement()">Test Template Replacement</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        function showCode(title, code) {
            const div = document.createElement('div');
            div.innerHTML = `<h3>${title}</h3><div class="code">${code.substring(0, 500)}...</div>`;
            document.getElementById('results').appendChild(div);
        }

        async function testTemplateReplacement() {
            document.getElementById('results').innerHTML = '';
            log('🔍 اختبار استبدال المتغيرات في القالب...', 'info');
            
            try {
                // 1. تحميل القالب مباشرة
                log('📥 تحميل القالب مباشرة...', 'info');
                const response = await fetch('./assets/modules/bugbounty/report_template.html');
                
                if (!response.ok) {
                    throw new Error(`فشل تحميل القالب: ${response.status}`);
                }
                
                const templateHTML = await response.text();
                log(`✅ تم تحميل القالب - الحجم: ${templateHTML.length} حرف`, 'success');
                
                // 2. فحص وجود المتغيرات
                const variables = [
                    '{{TARGET_URL}}',
                    '{{TOTAL_VULNERABILITIES}}',
                    '{{SECURITY_LEVEL}}',
                    '{{RISK_SCORE}}',
                    '{{HIGHEST_SEVERITY}}',
                    '{{VULNERABILITIES_CONTENT}}',
                    '{{TESTING_DETAILS}}',
                    '{{INTERACTIVE_DIALOGUES}}',
                    '{{VISUAL_CHANGES}}',
                    '{{PERSISTENT_RESULTS}}',
                    '{{IMPACT_VISUALIZATIONS}}',
                    '{{RECOMMENDATIONS_CONTENT}}',
                    '{{TIMESTAMP}}',
                    '{{IMAGES_COUNT}}'
                ];
                
                log('🔍 فحص وجود المتغيرات في القالب...', 'info');
                const foundVariables = [];
                const missingVariables = [];
                
                variables.forEach(variable => {
                    if (templateHTML.includes(variable)) {
                        foundVariables.push(variable);
                    } else {
                        missingVariables.push(variable);
                    }
                });
                
                log(`✅ متغيرات موجودة: ${foundVariables.length}/${variables.length}`, 'success');
                log(`❌ متغيرات مفقودة: ${missingVariables.length}`, missingVariables.length > 0 ? 'warning' : 'success');
                
                if (missingVariables.length > 0) {
                    log(`المتغيرات المفقودة: ${missingVariables.join(', ')}`, 'warning');
                }
                
                // 3. اختبار الاستبدال
                log('🔄 اختبار استبدال المتغيرات...', 'info');
                
                let processedTemplate = templateHTML;
                
                // استبدال المتغيرات بقيم تجريبية
                const testData = {
                    '{{TARGET_URL}}': 'https://example.com',
                    '{{TOTAL_VULNERABILITIES}}': '5',
                    '{{SECURITY_LEVEL}}': 'متوسط',
                    '{{RISK_SCORE}}': '75',
                    '{{HIGHEST_SEVERITY}}': 'عالي',
                    '{{VULNERABILITIES_CONTENT}}': '<h3>ثغرة XSS</h3><p>تم اكتشاف ثغرة XSS في الموقع</p>',
                    '{{TESTING_DETAILS}}': '<p>تم إجراء اختبارات شاملة</p>',
                    '{{INTERACTIVE_DIALOGUES}}': '<p>تم إجراء حوارات تفاعلية</p>',
                    '{{VISUAL_CHANGES}}': '<p>تم رصد تغيرات بصرية</p>',
                    '{{PERSISTENT_RESULTS}}': '<p>تم حفظ النتائج</p>',
                    '{{IMPACT_VISUALIZATIONS}}': '<p>تم توثيق التأثيرات</p>',
                    '{{RECOMMENDATIONS_CONTENT}}': '<p>يُنصح بإصلاح الثغرات</p>',
                    '{{TIMESTAMP}}': new Date().toLocaleString('ar'),
                    '{{IMAGES_COUNT}}': '3'
                };
                
                // تطبيق الاستبدالات
                Object.entries(testData).forEach(([variable, value]) => {
                    const regex = new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g');
                    processedTemplate = processedTemplate.replace(regex, value);
                });
                
                log('✅ تم تطبيق جميع الاستبدالات', 'success');
                
                // 4. فحص النتيجة
                const remainingVariables = variables.filter(variable => 
                    processedTemplate.includes(variable)
                );
                
                if (remainingVariables.length === 0) {
                    log('🎉 تم استبدال جميع المتغيرات بنجاح!', 'success');
                    log(`📊 حجم التقرير النهائي: ${processedTemplate.length} حرف`, 'success');
                    
                    // عرض جزء من النتيجة
                    showCode('جزء من التقرير المعالج', processedTemplate);
                    
                    // اختبار إنشاء ملف
                    log('💾 اختبار إنشاء ملف التقرير...', 'info');
                    const blob = new Blob([processedTemplate], { type: 'text/html;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'test-report.html';
                    link.textContent = 'تحميل التقرير التجريبي';
                    link.style.display = 'block';
                    link.style.margin = '10px 0';
                    link.style.padding = '10px';
                    link.style.background = '#007bff';
                    link.style.color = 'white';
                    link.style.textDecoration = 'none';
                    link.style.borderRadius = '5px';
                    
                    document.getElementById('results').appendChild(link);
                    
                    log('✅ تم إنشاء رابط التحميل بنجاح', 'success');
                    
                } else {
                    log(`❌ متغيرات لم يتم استبدالها: ${remainingVariables.join(', ')}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
