<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Progress Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #2c3e50; 
            color: white; 
        }
        .log { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            border-left: 4px solid; 
        }
        .success { background: #27ae60; border-color: #2ecc71; }
        .error { background: #e74c3c; border-color: #c0392b; }
        .info { background: #3498db; border-color: #2980b9; }
        button { 
            background: #e74c3c; 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px 0;
        }
        button:hover { background: #c0392b; }
    </style>
</head>
<body>
    <h1>📊 Progress Test v2</h1>
    <button onclick="runProgressTest()">🚀 Run Progress Test</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runProgressTest() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 بدء اختبار التقدم v2...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const core = new BugBountyCore();
                
                log('✅ تم تحميل BugBountyCore بنجاح', 'success');
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحميل القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار بسيط للثغرة
                const testVuln = { name: 'Progress Test XSS', type: 'xss', severity: 'High' };
                
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                const testData = {
                    vulnerabilities: [testVuln]
                };
                
                try {
                    // اختبار مع timeout قصير
                    const timeoutPromise = new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Timeout after 30 seconds')), 30000)
                    );
                    
                    const reportPromise = core.generateFinalComprehensiveReport(testData, [], 'https://progress-test.com');
                    
                    const report = await Promise.race([reportPromise, timeoutPromise]);
                    
                    if (report && report.length > 100) {
                        log(`✅ تم إنشاء التقرير بنجاح! الحجم: ${report.length} حرف`, 'success');
                        log('🎉 التقدم ممتاز - النظام يعمل!', 'success');
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ في التقرير: ${reportError.message}`, 'error');
                    
                    if (reportError.message.includes('includes')) {
                        log('❌ ما زالت مشكلة includes موجودة - نحتاج المزيد من الإصلاح!', 'error');
                    } else if (reportError.message.includes('Timeout')) {
                        log('⏰ انتهت المهلة الزمنية - قد يكون النظام يعمل ببطء', 'info');
                    } else {
                        log(`ℹ️ نوع خطأ مختلف: ${reportError.message}`, 'info');
                    }
                }
                
                log('📊 اختبار التقدم مكتمل!', 'success');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
