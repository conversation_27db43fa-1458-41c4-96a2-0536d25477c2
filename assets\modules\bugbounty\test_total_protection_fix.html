<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 TOTAL PROTECTION TEST - ULTIMATE FIX 🚀</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .error-section {
            background: rgba(255, 0, 0, 0.1);
            border-left: 5px solid #f44336;
        }
        .success-section {
            background: rgba(0, 255, 0, 0.1);
            border-left: 5px solid #4CAF50;
        }
        .log-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
            margin: 5px;
        }
        .status.success { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.warning { background: #ff9800; }
        .status.info { background: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TOTAL PROTECTION TEST - ULTIMATE FIX 🚀</h1>
            <p>اختبار شامل لجميع الإصلاحات المطبقة على نظام Bug Bounty v4.0</p>
        </div>

        <div class="test-section">
            <h2>📊 حالة الاختبار</h2>
            <div id="testStatus">
                <span class="status info">⏳ جاري التحضير...</span>
            </div>
            <div id="timeDisplay">⏱️ الوقت المنقضي: 0 ثانية</div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبارات الحماية</h2>
            <button class="btn" onclick="runTotalProtectionTest()">🚀 تشغيل TOTAL PROTECTION TEST</button>
            <button class="btn" onclick="testSafeFunctions()">🛡️ اختبار الدوال الآمنة</button>
            <button class="btn" onclick="testVulnTypeProcessing()">🔍 اختبار معالجة vulnType</button>
        </div>

        <div class="test-section">
            <h2>📝 سجل الاختبار</h2>
            <div id="testLog" class="log-output">جاري تحميل النظام...</div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج التفصيلية</h2>
            <div id="detailedResults" class="log-output">في انتظار تشغيل الاختبارات...</div>
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let startTime;
        let timeInterval;

        // تحميل النظام
        async function initializeSystem() {
            try {
                updateLog('🔄 تحميل BugBountyCore...');
                bugBountyCore = new BugBountyCore();
                updateLog('✅ تم تحميل BugBountyCore بنجاح');
                updateStatus('success', '✅ النظام جاهز');
                return true;
            } catch (error) {
                updateLog(`❌ خطأ في تحميل النظام: ${error.message}`);
                updateStatus('error', '❌ فشل التحميل');
                return false;
            }
        }

        // تشغيل اختبار الحماية الكامل
        async function runTotalProtectionTest() {
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            startTimer();
            updateStatus('info', '⏳ جاري تشغيل TOTAL PROTECTION TEST...');
            updateLog('🚀 بدء اختبار الحماية الكاملة (مهلة زمنية: 2 دقيقة)...');

            try {
                // اختبار safeToLowerCase
                updateLog('🧪 اختبار safeToLowerCase: xss test');
                const safeResult = bugBountyCore.safeToLowerCase('XSS Test');
                updateLog(`✅ دالة safeToLowerCase تعمل: ${safeResult._text}`);

                // اختبار includes
                updateLog('🧪 اختبار includes: true');
                const includesResult = safeResult.includes('xss');
                updateLog(`✅ دالة includes تعمل: ${includesResult}`);

                // تحميل القالب
                updateLog('📄 تحميل قالب التقرير...');
                const templateResponse = await fetch('./templates/report_template.html');
                const template = await templateResponse.text();
                updateLog(`✅ تم تحميل القالب: ${template.length} حرف`);

                // اختبار generateFinalComprehensiveReport
                updateLog('🧪 اختبار generateFinalComprehensiveReport (مهلة: 2 دقيقة)...');
                updateLog('⏳ يرجى الانتظار... تم تطبيق الحماية الكاملة التلقائية...');

                const testAnalysis = {
                    vulnerabilities: [
                        {
                            name: 'XSS Test Vulnerability',
                            type: 'xss',
                            severity: 'High',
                            description: 'Test vulnerability for protection testing'
                        }
                    ],
                    summary: {
                        total_vulnerabilities: 1,
                        high_severity: 1,
                        medium_severity: 0,
                        low_severity: 0
                    }
                };

                const testPages = [
                    {
                        url: 'https://example.com',
                        vulnerabilities: testAnalysis.vulnerabilities
                    }
                ];

                // تشغيل التقرير مع حماية من timeout
                const reportPromise = bugBountyCore.generateFinalComprehensiveReport(
                    testAnalysis, 
                    testPages, 
                    'https://example.com'
                );

                // إضافة timeout protection
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout after 2 minutes')), 120000);
                });

                const report = await Promise.race([reportPromise, timeoutPromise]);
                
                updateLog('✅ تم إنشاء التقرير بنجاح!');
                updateStatus('success', '🎉 TOTAL PROTECTION TEST نجح!');
                
                // عرض النتائج التفصيلية
                document.getElementById('detailedResults').textContent = 
                    `تم إنشاء تقرير بحجم: ${report.length} حرف\n` +
                    `عدد الثغرات المعالجة: ${testAnalysis.vulnerabilities.length}\n` +
                    `الوقت المستغرق: ${getElapsedTime()} ثانية\n` +
                    `حالة الحماية: ✅ مفعلة بالكامل`;

            } catch (error) {
                const elapsed = getElapsedTime();
                updateLog(`❌ خطأ في التقرير: ${error.message}`);
                updateLog(`⏱️ الوقت المستغرق: ${elapsed} ثانية`);
                
                if (error.message.includes('includes is not a function')) {
                    updateLog('❌ ما زالت مشكلة includes موجودة - نحتاج المزيد من الإصلاح!');
                    updateStatus('error', '❌ مشكلة includes مستمرة');
                } else if (error.message.includes('Timeout')) {
                    updateLog('⏰ انتهت المهلة الزمنية - قد يكون هناك حلقة لا نهائية');
                    updateStatus('warning', '⏰ انتهت المهلة الزمنية');
                } else {
                    updateLog(`❌ خطأ غير متوقع: ${error.message}`);
                    updateStatus('error', '❌ خطأ غير متوقع');
                }
            } finally {
                stopTimer();
                updateLog('🏆 TOTAL PROTECTION TEST مكتمل!');
            }
        }

        // اختبار الدوال الآمنة
        async function testSafeFunctions() {
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            updateLog('🛡️ اختبار الدوال الآمنة...');

            try {
                // اختبار safeToLowerCase مع قيم مختلفة
                const tests = [
                    'XSS',
                    'SQL Injection',
                    null,
                    undefined,
                    123,
                    { test: 'object' }
                ];

                tests.forEach((test, index) => {
                    try {
                        const result = bugBountyCore.safeToLowerCase(test);
                        updateLog(`✅ Test ${index + 1}: ${JSON.stringify(test)} → ${result._text}`);
                        
                        // اختبار includes
                        const includesTest = result.includes('test');
                        updateLog(`   includes('test'): ${includesTest}`);
                    } catch (error) {
                        updateLog(`❌ Test ${index + 1} failed: ${error.message}`);
                    }
                });

                updateStatus('success', '✅ اختبار الدوال الآمنة مكتمل');
            } catch (error) {
                updateLog(`❌ خطأ في اختبار الدوال الآمنة: ${error.message}`);
                updateStatus('error', '❌ فشل اختبار الدوال الآمنة');
            }
        }

        // اختبار معالجة vulnType
        async function testVulnTypeProcessing() {
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            updateLog('🔍 اختبار معالجة vulnType...');

            try {
                const vulnTypes = [
                    'xss',
                    'SQL Injection',
                    'Command Injection',
                    null,
                    undefined,
                    { type: 'object' }
                ];

                vulnTypes.forEach((vulnType, index) => {
                    try {
                        const safeType = bugBountyCore.getSafeVulnType({ type: vulnType });
                        updateLog(`✅ VulnType ${index + 1}: ${JSON.stringify(vulnType)} → ${safeType._text}`);
                        
                        // اختبار safeIncludes
                        const includesTest = bugBountyCore.safeIncludes(safeType, 'sql');
                        updateLog(`   safeIncludes('sql'): ${includesTest}`);
                    } catch (error) {
                        updateLog(`❌ VulnType ${index + 1} failed: ${error.message}`);
                    }
                });

                updateStatus('success', '✅ اختبار معالجة vulnType مكتمل');
            } catch (error) {
                updateLog(`❌ خطأ في اختبار معالجة vulnType: ${error.message}`);
                updateStatus('error', '❌ فشل اختبار معالجة vulnType');
            }
        }

        // دوال مساعدة
        function updateLog(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById('testLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(type, message) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<span class="status ${type}">${message}</span>`;
        }

        function startTimer() {
            startTime = Date.now();
            timeInterval = setInterval(() => {
                const elapsed = getElapsedTime();
                document.getElementById('timeDisplay').textContent = `⏱️ الوقت المنقضي: ${elapsed} ثانية`;
            }, 1000);
        }

        function stopTimer() {
            if (timeInterval) {
                clearInterval(timeInterval);
                timeInterval = null;
            }
        }

        function getElapsedTime() {
            return Math.floor((Date.now() - startTime) / 1000);
        }

        // تحميل النظام عند بدء الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
