<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test New File</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Test New File</h1>
    <button onclick="testNewFile()">Test New File</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function testNewFile() {
            document.getElementById('results').innerHTML = '';
            log('🔍 اختبار الملف الجديد...', 'info');
            
            try {
                // Load new test file
                const script = document.createElement('script');
                script.src = './test_new_core.js';
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف الجديد بنجاح', 'success');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل الملف الجديد', 'error');
                        reject(new Error('Failed to load script'));
                    };
                    document.head.appendChild(script);
                });
                
                // Wait for initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Check if class exists
                if (typeof TestBugBountyCore === 'undefined') {
                    log('❌ الكلاس TestBugBountyCore غير موجود', 'error');
                    return;
                }
                log('✅ الكلاس TestBugBountyCore موجود', 'success');
                
                // Check prototype methods
                const prototypeMethods = Object.getOwnPropertyNames(TestBugBountyCore.prototype);
                log(`📋 عدد الدوال في prototype: ${prototypeMethods.length}`, 'info');
                
                const hasLoadMethod = prototypeMethods.includes('loadReportTemplate');
                const hasTestMethod = prototypeMethods.includes('testMethod');
                
                log(`🔍 loadReportTemplate: ${hasLoadMethod ? '✅ موجود' : '❌ غير موجود'}`, hasLoadMethod ? 'success' : 'error');
                log(`🔍 testMethod: ${hasTestMethod ? '✅ موجود' : '❌ غير موجود'}`, hasTestMethod ? 'success' : 'error');
                
                if (hasLoadMethod) {
                    // Create instance
                    try {
                        const core = new TestBugBountyCore();
                        log('✅ تم إنشاء المثيل بنجاح', 'success');
                        
                        // Test the method
                        if (typeof core.loadReportTemplate === 'function') {
                            log('✅ دالة loadReportTemplate متاحة في المثيل', 'success');
                            
                            // Try to call it
                            try {
                                await core.loadReportTemplate();
                                
                                if (core.reportTemplateHTML && core.reportTemplateHTML.length > 100) {
                                    log(`✅ تم تحميل القالب بنجاح! الحجم: ${core.reportTemplateHTML.length}`, 'success');
                                    log('🎉 الاختبار مكتمل بنجاح!', 'success');
                                } else {
                                    log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                                }
                            } catch (methodError) {
                                log(`❌ خطأ في استدعاء الدالة: ${methodError.message}`, 'error');
                            }
                        } else {
                            log('❌ دالة loadReportTemplate غير متاحة في المثيل', 'error');
                        }
                        
                    } catch (constructorError) {
                        log(`❌ خطأ في إنشاء المثيل: ${constructorError.message}`, 'error');
                    }
                } else {
                    log('❌ الدالة غير موجودة في prototype', 'error');
                    log(`📋 الدوال الموجودة: ${prototypeMethods.join(', ')}`, 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
