<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Quick Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Quick Fix Test</h1>
    <button onclick="runQuickTest()">Run Quick Test</button>
    
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runQuickTest() {
            document.getElementById('results').innerHTML = '';
            
            log('🔍 اختبار سريع للإصلاح...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const core = new BugBountyCore();
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحضير النظام - القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار مع البيانات التي فشلت سابقاً
                const testData = {
                    total_vulnerabilities: 1,
                    vulnerabilities: [
                        { name: 'Test XSS', severity: 'High', description: 'Test vulnerability' }
                    ]
                };
                
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    
                    if (report && report.length > 1000) {
                        log(`🎉 نجح! حجم التقرير: ${report.length} حرف`, 'success');
                        
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length}`, 'info');
                        } else {
                            log('✅ تم استبدال جميع المتغيرات!', 'success');
                        }
                        
                        // إنشاء رابط تحميل
                        const blob = new Blob([report], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'quick-fix-test-report.html';
                        link.textContent = '🎉 تحميل التقرير الناجح!';
                        link.style.display = 'block';
                        link.style.margin = '10px 0';
                        link.style.padding = '15px';
                        link.style.background = '#28a745';
                        link.style.color = 'white';
                        link.style.textDecoration = 'none';
                        link.style.borderRadius = '5px';
                        link.style.fontSize = '16px';
                        link.style.fontWeight = 'bold';
                        
                        document.getElementById('results').appendChild(link);
                        
                        log('🎉 تم إصلاح المشكلة نهائياً!', 'success');
                        log('✅ النظام جاهز للاستخدام الفعلي', 'success');
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ: ${reportError.message}`, 'error');
                    
                    if (reportError.message.includes('forEach')) {
                        log('⚠️ ما زالت هناك مشكلة forEach - يحتاج إصلاح إضافي', 'error');
                    }
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
