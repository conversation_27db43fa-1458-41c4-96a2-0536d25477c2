<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي شامل - نظام Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار نهائي شامل - نظام Bug Bounty v4.0</h1>
            <p>اختبار شامل للتحقق من إصلاح جميع المشاكل وعمل النظام بشكل كامل</p>
        </div>

        <div class="test-section">
            <h2>🎯 اختبار القالب المدمج</h2>
            <p>اختبار تحميل واستخدام القالب المدمج في النظام</p>
            <button class="test-button" onclick="testEmbeddedTemplate()">اختبار القالب المدمج</button>
            <div id="templateResult" class="result info" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>📄 اختبار تقرير الصفحة المنفصل</h2>
            <p>اختبار إنشاء تقرير منفصل للصفحة باستخدام القالب المدمج</p>
            <button class="test-button" onclick="testPageReport()">اختبار تقرير الصفحة</button>
            <div id="pageReportResult" class="result info" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 اختبار التقرير الرئيسي الشامل</h2>
            <p>اختبار إنشاء التقرير الرئيسي الشامل باستخدام القالب المدمج</p>
            <button class="test-button" onclick="testMainReport()">اختبار التقرير الرئيسي</button>
            <div id="mainReportResult" class="result info" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>🔍 اختبار شامل للنظام</h2>
            <p>اختبار شامل لجميع وظائف النظام مع عرض التقدم</p>
            <button class="test-button" onclick="runComprehensiveTest()">تشغيل الاختبار الشامل</button>
            <div class="progress" style="display:none;" id="testProgress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="comprehensiveResult" class="result info" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>📋 نتائج الاختبار النهائية</h2>
            <div id="finalResults" class="result info" style="display:none;"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testResults = {
            embeddedTemplate: false,
            pageReport: false,
            mainReport: false,
            comprehensive: false
        };

        // تهيئة النظام
        async function initializeSystem() {
            try {
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة نظام Bug Bounty Core بنجاح');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                return false;
            }
        }

        // اختبار القالب المدمج
        async function testEmbeddedTemplate() {
            const resultDiv = document.getElementById('templateResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار القالب المدمج...';

            try {
                if (!bugBountyCore) {
                    const initialized = await initializeSystem();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }

                // اختبار تحميل القالب المدمج
                const template = await bugBountyCore.preloadTemplate();
                
                if (template && template.length > 100) {
                    testResults.embeddedTemplate = true;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ نجح اختبار القالب المدمج!\n` +
                                          `📏 حجم القالب: ${template.length} حرف\n` +
                                          `🎯 القالب محفوظ في الذاكرة: ${bugBountyCore.cachedTemplate ? 'نعم' : 'لا'}\n` +
                                          `📄 أول 200 حرف: ${template.substring(0, 200)}...`;
                } else {
                    throw new Error('القالب المدمج فارغ أو غير صالح');
                }
            } catch (error) {
                testResults.embeddedTemplate = false;
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل اختبار القالب المدمج: ${error.message}`;
            }
        }

        // اختبار تقرير الصفحة المنفصل
        async function testPageReport() {
            const resultDiv = document.getElementById('pageReportResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار تقرير الصفحة المنفصل...';

            try {
                if (!bugBountyCore) {
                    const initialized = await initializeSystem();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }

                // بيانات تجريبية للاختبار
                const testPageResult = {
                    vulnerabilities: [
                        { name: 'XSS Test', severity: 'high', description: 'اختبار XSS' },
                        { name: 'SQL Injection Test', severity: 'critical', description: 'اختبار SQL Injection' }
                    ]
                };

                const pageReport = await bugBountyCore.generatePageHTMLReport(
                    testPageResult, 
                    'https://example.com', 
                    1
                );

                if (pageReport && pageReport.length > 100) {
                    testResults.pageReport = true;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ نجح اختبار تقرير الصفحة المنفصل!\n` +
                                          `📏 حجم التقرير: ${pageReport.length} حرف\n` +
                                          `🎯 يحتوي على القالب: ${pageReport.includes('<!DOCTYPE html>') ? 'نعم' : 'لا'}\n` +
                                          `📄 يحتوي على البيانات: ${pageReport.includes('example.com') ? 'نعم' : 'لا'}`;
                } else {
                    throw new Error('تقرير الصفحة فارغ أو غير صالح');
                }
            } catch (error) {
                testResults.pageReport = false;
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل اختبار تقرير الصفحة: ${error.message}`;
            }
        }

        // اختبار التقرير الرئيسي الشامل
        async function testMainReport() {
            const resultDiv = document.getElementById('mainReportResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار التقرير الرئيسي الشامل...';

            try {
                if (!bugBountyCore) {
                    const initialized = await initializeSystem();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }

                // بيانات تجريبية للاختبار
                const testAnalysis = {
                    total_vulnerabilities: 5,
                    high_severity_count: 2,
                    medium_severity_count: 2,
                    low_severity_count: 1,
                    security_score: 75
                };

                const testPageResults = [
                    {
                        page_url: 'https://example.com/page1',
                        vulnerabilities: [
                            { name: 'XSS Test 1', severity: 'high' },
                            { name: 'CSRF Test 1', severity: 'medium' }
                        ]
                    },
                    {
                        page_url: 'https://example.com/page2',
                        vulnerabilities: [
                            { name: 'SQL Injection Test', severity: 'critical' },
                            { name: 'File Upload Test', severity: 'low' }
                        ]
                    }
                ];

                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(
                    testAnalysis,
                    testPageResults,
                    'https://example.com'
                );

                if (mainReport && mainReport.length > 100) {
                    testResults.mainReport = true;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ نجح اختبار التقرير الرئيسي الشامل!\n` +
                                          `📏 حجم التقرير: ${mainReport.length} حرف\n` +
                                          `🎯 يحتوي على القالب: ${mainReport.includes('<!DOCTYPE html>') ? 'نعم' : 'لا'}\n` +
                                          `📊 يحتوي على الإحصائيات: ${mainReport.includes('5') ? 'نعم' : 'لا'}`;
                } else {
                    throw new Error('التقرير الرئيسي فارغ أو غير صالح');
                }
            } catch (error) {
                testResults.mainReport = false;
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل اختبار التقرير الرئيسي: ${error.message}`;
            }
        }

        // تشغيل الاختبار الشامل
        async function runComprehensiveTest() {
            const resultDiv = document.getElementById('comprehensiveResult');
            const progressDiv = document.getElementById('testProgress');
            const progressBar = document.getElementById('progressBar');
            const finalDiv = document.getElementById('finalResults');

            resultDiv.style.display = 'block';
            progressDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 بدء الاختبار الشامل...\n';

            try {
                // خطوة 1: اختبار القالب المدمج
                progressBar.style.width = '25%';
                resultDiv.textContent += '📋 خطوة 1/4: اختبار القالب المدمج...\n';
                await testEmbeddedTemplate();
                
                // خطوة 2: اختبار تقرير الصفحة
                progressBar.style.width = '50%';
                resultDiv.textContent += '📄 خطوة 2/4: اختبار تقرير الصفحة...\n';
                await testPageReport();
                
                // خطوة 3: اختبار التقرير الرئيسي
                progressBar.style.width = '75%';
                resultDiv.textContent += '📊 خطوة 3/4: اختبار التقرير الرئيسي...\n';
                await testMainReport();
                
                // خطوة 4: تجميع النتائج
                progressBar.style.width = '100%';
                resultDiv.textContent += '📋 خطوة 4/4: تجميع النتائج النهائية...\n';
                
                testResults.comprehensive = testResults.embeddedTemplate && 
                                          testResults.pageReport && 
                                          testResults.mainReport;

                // عرض النتائج النهائية
                const successCount = Object.values(testResults).filter(result => result).length;
                const totalTests = Object.keys(testResults).length;
                
                finalDiv.style.display = 'block';
                if (testResults.comprehensive) {
                    finalDiv.className = 'result success';
                    finalDiv.textContent = `🎉 نجح الاختبار الشامل بالكامل!\n\n` +
                                         `✅ جميع الاختبارات نجحت (${successCount}/${totalTests})\n` +
                                         `✅ القالب المدمج: ${testResults.embeddedTemplate ? 'نجح' : 'فشل'}\n` +
                                         `✅ تقرير الصفحة: ${testResults.pageReport ? 'نجح' : 'فشل'}\n` +
                                         `✅ التقرير الرئيسي: ${testResults.mainReport ? 'نجح' : 'فشل'}\n\n` +
                                         `🔧 تم إصلاح جميع المشاكل بنجاح!\n` +
                                         `🎯 النظام جاهز للاستخدام الكامل`;
                } else {
                    finalDiv.className = 'result warning';
                    finalDiv.textContent = `⚠️ الاختبار الشامل مكتمل مع بعض المشاكل\n\n` +
                                         `📊 نجح ${successCount} من ${totalTests} اختبارات\n` +
                                         `${testResults.embeddedTemplate ? '✅' : '❌'} القالب المدمج\n` +
                                         `${testResults.pageReport ? '✅' : '❌'} تقرير الصفحة\n` +
                                         `${testResults.mainReport ? '✅' : '❌'} التقرير الرئيسي\n\n` +
                                         `🔧 يرجى مراجعة الاختبارات الفاشلة`;
                }

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n✅ اكتمل الاختبار الشامل!';

            } catch (error) {
                progressBar.style.width = '100%';
                resultDiv.className = 'result error';
                resultDiv.textContent += `\n❌ خطأ في الاختبار الشامل: ${error.message}`;
                
                finalDiv.style.display = 'block';
                finalDiv.className = 'result error';
                finalDiv.textContent = `❌ فشل الاختبار الشامل: ${error.message}`;
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', async () => {
            console.log('🚀 بدء تهيئة نظام الاختبار...');
            await initializeSystem();
            console.log('✅ تم تهيئة نظام الاختبار بنجاح');
        });
    </script>
</body>
</html>
