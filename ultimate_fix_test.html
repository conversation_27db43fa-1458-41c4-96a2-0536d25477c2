<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ultimate Fix Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #2c3e50; 
            color: white; 
        }
        .log { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            border-left: 4px solid; 
        }
        .success { background: #27ae60; border-color: #2ecc71; }
        .error { background: #e74c3c; border-color: #c0392b; }
        .info { background: #3498db; border-color: #2980b9; }
        button { 
            background: linear-gradient(45deg, #e74c3c, #f39c12, #27ae60, #3498db);
            color: white; 
            border: none; 
            padding: 20px 40px; 
            border-radius: 10px; 
            cursor: pointer; 
            font-size: 18px; 
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        button:hover { 
            transform: scale(1.05); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            font-size: 3em;
            background: linear-gradient(45deg, #e74c3c, #f39c12, #27ae60, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <h1>🔥 ULTIMATE FIX 🔥</h1>
    <div style="text-align: center;">
        <button onclick="runUltimateFixTest()">🚀 ULTIMATE FIX TEST 🚀</button>
    </div>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runUltimateFixTest() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 بدء الاختبار النهائي المطلق...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const core = new BugBountyCore();
                
                log('✅ تم تحميل BugBountyCore بنجاح', 'success');
                
                // اختبار دالة safeToLowerCase الجديدة
                const testResult = core.safeToLowerCase('XSS Test');
                log(`🧪 اختبار safeToLowerCase: ${testResult}`, 'info');
                
                // اختبار includes
                if (testResult.includes && typeof testResult.includes === 'function') {
                    const includesResult = testResult.includes('xss');
                    log(`✅ دالة includes تعمل: ${includesResult}`, 'success');
                } else {
                    log('❌ دالة includes لا تعمل', 'error');
                }
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحميل القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار بسيط للثغرة
                const testVuln = { name: 'Ultimate Fix XSS', type: 'xss', severity: 'High' };
                
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                const testData = {
                    vulnerabilities: [testVuln]
                };
                
                try {
                    // اختبار مع timeout قصير
                    const timeoutPromise = new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Timeout after 20 seconds')), 20000)
                    );
                    
                    const reportPromise = core.generateFinalComprehensiveReport(testData, [], 'https://ultimate-fix.com');
                    
                    const report = await Promise.race([reportPromise, timeoutPromise]);
                    
                    if (report && report.length > 100) {
                        log(`🎉 نجح! حجم التقرير: ${report.length} حرف`, 'success');
                        log('🔥 ULTIMATE FIX نجح! النظام يعمل!', 'success');
                        
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length}`, 'info');
                        } else {
                            log('✅ تم استبدال جميع المتغيرات!', 'success');
                        }
                        
                        // إنشاء رابط تحميل
                        const blob = new Blob([report], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'ultimate-fix-report.html';
                        link.textContent = '🔥 تحميل تقرير ULTIMATE FIX!';
                        link.style.cssText = `
                            display: block;
                            margin: 20px auto;
                            padding: 20px 40px;
                            background: linear-gradient(45deg, #27ae60, #3498db);
                            color: white;
                            text-decoration: none;
                            border-radius: 10px;
                            font-size: 18px;
                            font-weight: bold;
                            text-align: center;
                            width: 300px;
                        `;
                        
                        document.getElementById('results').appendChild(link);
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ في التقرير: ${reportError.message}`, 'error');
                    
                    if (reportError.message.includes('includes')) {
                        log('❌ ما زالت مشكلة includes موجودة!', 'error');
                    } else if (reportError.message.includes('Timeout')) {
                        log('⏰ انتهت المهلة الزمنية - قد يكون النظام يعمل ببطء', 'info');
                    } else {
                        log(`ℹ️ نوع خطأ مختلف: ${reportError.message}`, 'info');
                    }
                }
                
                log('🔥 ULTIMATE FIX TEST مكتمل!', 'success');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
