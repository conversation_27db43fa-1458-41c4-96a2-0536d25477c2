<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Detailed Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Detailed Debug Test</h1>
    <button onclick="runDetailedTest()">Run Detailed Test</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function runDetailedTest() {
            document.getElementById('results').innerHTML = '';
            log('🔍 بدء الاختبار التفصيلي...', 'info');
            
            try {
                // Test 1: Load the script
                log('📥 تحميل ملف BugBountyCore.js...', 'info');
                
                const script = document.createElement('script');
                // إضافة معرف فريد لتجنب التخزين المؤقت
                const timestamp = new Date().getTime();
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${timestamp}&nocache=${Math.random()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف بنجاح', 'success');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل الملف', 'error');
                        reject(new Error('Failed to load script'));
                    };
                    document.head.appendChild(script);
                });
                
                // Wait a bit for the script to initialize
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Test 2: Check if class exists
                log('🔍 فحص وجود الكلاس...', 'info');
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ الكلاس BugBountyCore غير موجود', 'error');
                    return;
                }
                log('✅ الكلاس BugBountyCore موجود', 'success');
                
                // Test 3: Check prototype methods
                log('🔍 فحص دوال prototype...', 'info');
                const prototypeMethods = Object.getOwnPropertyNames(BugBountyCore.prototype);
                log(`📋 عدد الدوال في prototype: ${prototypeMethods.length}`, 'info');
                
                const hasLoadMethod = prototypeMethods.includes('loadReportTemplate');
                log(`🔍 دالة loadReportTemplate في prototype: ${hasLoadMethod}`, hasLoadMethod ? 'success' : 'error');
                
                if (hasLoadMethod) {
                    log('✅ الدالة موجودة في prototype', 'success');
                } else {
                    log('❌ الدالة غير موجودة في prototype', 'error');
                    log(`📋 الدوال الموجودة: ${prototypeMethods.slice(0, 10).join(', ')}...`, 'info');
                }
                
                // Test 4: Create instance
                log('🏗️ إنشاء مثيل من الكلاس...', 'info');
                let core;
                try {
                    core = new BugBountyCore();
                    log('✅ تم إنشاء المثيل بنجاح', 'success');
                } catch (constructorError) {
                    log(`❌ خطأ في إنشاء المثيل: ${constructorError.message}`, 'error');
                    return;
                }
                
                // Test 5: Check instance methods
                log('🔍 فحص دوال المثيل...', 'info');
                
                // Check if method exists on instance
                if (typeof core.loadReportTemplate === 'function') {
                    log('✅ دالة loadReportTemplate موجودة في المثيل', 'success');
                    
                    // Test 6: Try to call the method
                    log('🚀 محاولة استدعاء الدالة...', 'info');
                    try {
                        await core.loadReportTemplate();
                        
                        if (core.reportTemplateHTML && core.reportTemplateHTML.length > 100) {
                            log(`✅ تم تحميل القالب بنجاح! الحجم: ${core.reportTemplateHTML.length}`, 'success');
                            log('🎉 الاختبار مكتمل بنجاح!', 'success');
                        } else {
                            log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                        }
                    } catch (methodError) {
                        log(`❌ خطأ في استدعاء الدالة: ${methodError.message}`, 'error');
                        console.error('تفاصيل الخطأ:', methodError);
                    }
                } else {
                    log('❌ دالة loadReportTemplate غير موجودة في المثيل', 'error');
                    
                    // Debug: Check what methods are available
                    const instanceMethods = [];
                    for (let prop in core) {
                        if (typeof core[prop] === 'function') {
                            instanceMethods.push(prop);
                        }
                    }
                    log(`📋 دوال المثيل: ${instanceMethods.slice(0, 10).join(', ')}...`, 'info');
                    
                    // Check prototype chain
                    const proto = Object.getPrototypeOf(core);
                    const protoMethods = Object.getOwnPropertyNames(proto);
                    log(`📋 دوال prototype chain: ${protoMethods.slice(0, 10).join(', ')}...`, 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ عام في الاختبار: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
