<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار بسيط - تحميل القالب</title>
    <style>
        body { font-family: Arial; margin: 20px; direction: rtl; }
        .result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 اختبار بسيط - تحميل القالب</h1>
    <button onclick="testSimple()">اختبار بسيط</button>
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }

        async function testSimple() {
            log('🔍 بدء الاختبار البسيط...', 'info');

            try {
                // اختبار عدة مسارات مختلفة
                const paths = [
                    './assets/modules/bugbounty/report_template.html',
                    '/assets/modules/bugbounty/report_template.html',
                    'assets/modules/bugbounty/report_template.html'
                ];

                let successfulPath = null;
                let response = null;

                for (const path of paths) {
                    try {
                        log(`📡 اختبار المسار: ${path}`, 'info');
                        response = await fetch(path);
                        log(`📊 استجابة: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                        if (response.ok) {
                            successfulPath = path;
                            break;
                        }
                    } catch (pathError) {
                        log(`❌ خطأ في المسار ${path}: ${pathError.message}`, 'error');
                    }
                }

                if (!successfulPath) {
                    throw new Error('فشل في الوصول للقالب بجميع المسارات المختبرة');
                }
                
                log(`✅ تم العثور على القالب في المسار: ${successfulPath}`, 'success');
                const content = await response.text();
                log(`📄 حجم المحتوى: ${content.length} حرف`, 'success');

                const hasDoctype = content.includes('<!DOCTYPE html>');
                const hasTitle = content.includes('تقرير Bug Bounty');
                log(`🔍 فحص المحتوى: DOCTYPE=${hasDoctype}, Title=${hasTitle}`, 'info');

                if (hasDoctype && hasTitle) {
                    log('✅ القالب صالح!', 'success');
                } else {
                    log('❌ القالب غير صالح', 'error');
                }
                
                // اختبار تحميل BugBountyCore
                log('🔧 اختبار تحميل BugBountyCore...', 'info');
                
                // تحميل الملف أولاً
                const script = document.createElement('script');
                script.src = 'assets/modules/bugbounty/BugBountyCore.js';
                
                script.onload = async function() {
                    log('✅ تم تحميل ملف BugBountyCore.js', 'success');
                    
                    try {
                        // انتظار قليل للتأكد من تحميل الكلاس
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        if (typeof BugBountyCore !== 'undefined') {
                            log('✅ كلاس BugBountyCore متاح', 'success');

                            // فحص prototype الكلاس قبل إنشاء المثيل
                            const prototypeMethods = Object.getOwnPropertyNames(BugBountyCore.prototype);
                            log(`🔍 دوال الكلاس: ${prototypeMethods.join(', ')}`, 'info');

                            const hasLoadMethod = prototypeMethods.includes('loadReportTemplate');
                            log(`🔍 دالة loadReportTemplate في prototype: ${hasLoadMethod}`, hasLoadMethod ? 'success' : 'error');

                            try {
                                const core = new BugBountyCore();
                                log('✅ تم إنشاء مثيل BugBountyCore', 'success');

                                // انتظار قليل للتأكد من اكتمال التهيئة
                                await new Promise(resolve => setTimeout(resolve, 2000));

                                // فحص وجود الدالة في المثيل
                                if (typeof core.loadReportTemplate === 'function') {
                                    log('✅ دالة loadReportTemplate موجودة في المثيل', 'success');

                                    // اختبار الدالة
                                    try {
                                        await core.loadReportTemplate();

                                        if (core.reportTemplateHTML && core.reportTemplateHTML.length > 100) {
                                            log(`✅ تم تحميل القالب بنجاح! الحجم: ${core.reportTemplateHTML.length}`, 'success');
                                        } else {
                                            log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                                        }
                                    } catch (loadError) {
                                        log(`❌ خطأ في تشغيل loadReportTemplate: ${loadError.message}`, 'error');
                                    }

                                } else {
                                    log('❌ دالة loadReportTemplate غير موجودة في المثيل', 'error');

                                    // فحص جميع الدوال المتاحة في المثيل
                                    const instanceMethods = [];
                                    for (let prop in core) {
                                        if (typeof core[prop] === 'function') {
                                            instanceMethods.push(prop);
                                        }
                                    }
                                    log(`🔍 دوال المثيل: ${instanceMethods.join(', ')}`, 'info');

                                    // فحص prototype chain
                                    const protoMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(core));
                                    log(`🔍 دوال prototype: ${protoMethods.join(', ')}`, 'info');
                                }

                            } catch (constructorError) {
                                log(`❌ خطأ في إنشاء مثيل BugBountyCore: ${constructorError.message}`, 'error');
                                console.error('تفاصيل خطأ الكونستركتور:', constructorError);
                            }
                            
                        } else {
                            log('❌ كلاس BugBountyCore غير متاح', 'error');
                        }
                        
                    } catch (error) {
                        log(`❌ خطأ في اختبار BugBountyCore: ${error.message}`, 'error');
                        console.error('تفاصيل الخطأ:', error);
                    }
                };
                
                script.onerror = function() {
                    log('❌ فشل في تحميل ملف BugBountyCore.js', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
