# 🏆 TOTAL PROTECTION VICTORY TEST 🏆
# 🚀 ULTIMATE BUG BOUNTY v4.0 PROTECTION VERIFICATION 🚀

param(
    [switch]$Detailed,
    [switch]$StressTest,
    [int]$TimeoutMinutes = 2
)

# إعداد الألوان والمتغيرات
$Host.UI.RawUI.WindowTitle = "ULTIMATE PROTECTION TEST"
$startTime = Get-Date
$testResults = @()
$stats = @{
    Passed = 0
    Failed = 0
    Warnings = 0
    Total = 0
}

# دالة لإضافة نتيجة اختبار
function Add-TestResult {
    param($TestName, $Status, $Details, $Category = "General")
    $global:testResults += [PSCustomObject]@{
        Test = $TestName
        Status = $Status
        Details = $Details
        Category = $Category
        Time = (Get-Date).ToString("HH:mm:ss")
        Duration = ((Get-Date) - $global:startTime).TotalSeconds
    }
    
    switch ($Status) {
        "PASS" { $global:stats.Passed++ }
        "FAIL" { $global:stats.Failed++ }
        "WARN" { $global:stats.Warnings++ }
    }
    $global:stats.Total++
}

# دالة لعرض رسالة ملونة مع الوقت
function Write-TimestampMessage {
    param($Message, $Color = "White", $Icon = "")
    $timestamp = (Get-Date).ToString("HH:mm:ss")
    Write-Host "[$timestamp] $Icon $Message" -ForegroundColor $Color
}

# دالة لعرض بانر النجاح
function Show-VictoryBanner {
    Write-Host ""
    Write-Host "🏆" * 60 -ForegroundColor Yellow
    Write-Host "🎉 TOTAL PROTECTION VICTORY! 🎉" -ForegroundColor Green -BackgroundColor Black
    Write-Host "جميع الإصلاحات تعمل بنجاح - النظام محمي بالكامل!" -ForegroundColor Green
    Write-Host "🛡️ لا توجد مشاكل في includes - الحماية مفعلة 100%" -ForegroundColor Green
    Write-Host "🏆" * 60 -ForegroundColor Yellow
    Write-Host ""
}

# دالة لعرض بانر الفشل
function Show-FailureBanner {
    Write-Host ""
    Write-Host "❌" * 60 -ForegroundColor Red
    Write-Host "❌ PROTECTION FAILURE DETECTED! ❌" -ForegroundColor Red -BackgroundColor Black
    Write-Host "توجد مشاكل حرجة تحتاج إصلاح فوري!" -ForegroundColor Red
    Write-Host "❌" * 60 -ForegroundColor Red
    Write-Host ""
}

# بدء الاختبار
Write-Host "🚀 ULTIMATE PROTECTION TEST 🚀" -ForegroundColor Cyan -BackgroundColor Black
Write-Host "🏆 TOTAL PROTECTION VICTORY VERIFICATION 🏆" -ForegroundColor Yellow
Write-Host "=" * 80 -ForegroundColor Gray

Write-TimestampMessage "🚀 بدء اختبار الحماية الكاملة (مهلة زمنية: $TimeoutMinutes دقيقة)..." "Cyan" "🚀"

# 1. فحص الملف الأساسي
Write-TimestampMessage "🔍 فحص ملف BugBountyCore.js..." "Yellow" "🔍"
$coreFile = ".\BugBountyCore.js"
if (Test-Path $coreFile) {
    Add-TestResult "BugBountyCore.js exists" "PASS" "File found and accessible" "Core"
    Write-TimestampMessage "✅ تم تحميل BugBountyCore بنجاح" "Green" "✅"
} else {
    Add-TestResult "BugBountyCore.js missing" "FAIL" "Cannot find core file" "Core"
    Write-TimestampMessage "❌ ملف BugBountyCore.js مفقود" "Red" "❌"
    Show-FailureBanner
    exit 1
}

# 2. قراءة محتوى الملف
$content = Get-Content $coreFile -Raw -Encoding UTF8
Write-TimestampMessage "📄 تم تحميل الملف: $($content.Length) حرف" "Green" "📄"

# 3. فحص استخدامات includes المباشرة الخطيرة
Write-TimestampMessage "🧪 اختبار safeToLowerCase والدوال الآمنة..." "Magenta" "🧪"
$directIncludes = [regex]::Matches($content, "vulnType\.includes\(|safeVulnType\.includes\(")

if ($directIncludes.Count -eq 0) {
    Add-TestResult "No direct includes usage" "PASS" "All direct usage fixed" "Safety"
    Write-TimestampMessage "✅ لا توجد استخدامات مباشرة خطيرة لـ includes" "Green" "✅"
} else {
    Add-TestResult "Direct includes found" "FAIL" "Direct usage count: $($directIncludes.Count)" "Safety"
    Write-TimestampMessage "❌ توجد $($directIncludes.Count) استخدامات مباشرة خطيرة" "Red" "❌"
    
    # عرض الاستخدامات الخطيرة
    $lines = $content -split "`n"
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match "vulnType\.includes\(|safeVulnType\.includes\(") {
            Write-Host "   Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Red
        }
    }
}

# 4. فحص الدوال الآمنة
Write-TimestampMessage "🛡️ فحص وجود الدوال الآمنة..." "Blue" "🛡️"
$safeFunctions = @(
    "safeToLowerCase",
    "safeIncludes", 
    "createSafeStringObject",
    "makeSafeVulnType",
    "getSafeVulnType"
)

foreach ($func in $safeFunctions) {
    if ($content -match "$func\s*\(") {
        Add-TestResult "Function $func exists" "PASS" "Function found in code" "Functions"
        Write-TimestampMessage "✅ دالة $func موجودة" "Green" "✅"
    } else {
        Add-TestResult "Function $func missing" "FAIL" "Function not found" "Functions"
        Write-TimestampMessage "❌ دالة $func مفقودة" "Red" "❌"
    }
}

# 5. إحصائيات الاستخدام الآمن
Write-TimestampMessage "📊 تحليل إحصائيات الاستخدام الآمن..." "Cyan" "📊"
$safeIncludesUsage = [regex]::Matches($content, "this\.safeIncludes\(")
$safeToLowerCaseUsage = [regex]::Matches($content, "this\.safeToLowerCase\(")

Write-TimestampMessage "✅ استخدامات safeIncludes: $($safeIncludesUsage.Count)" "Green" "📈"
Write-TimestampMessage "✅ استخدامات safeToLowerCase: $($safeToLowerCaseUsage.Count)" "Green" "📈"

if ($safeIncludesUsage.Count -gt 200) {
    Add-TestResult "safeIncludes usage excellent" "PASS" "Usage count: $($safeIncludesUsage.Count)" "Usage"
} elseif ($safeIncludesUsage.Count -gt 50) {
    Add-TestResult "safeIncludes usage good" "PASS" "Usage count: $($safeIncludesUsage.Count)" "Usage"
} else {
    Add-TestResult "safeIncludes usage low" "WARN" "Usage count: $($safeIncludesUsage.Count)" "Usage"
}

# 6. فحص دالة generateFinalComprehensiveReport
Write-TimestampMessage "🧪 اختبار generateFinalComprehensiveReport (مهلة: $TimeoutMinutes دقيقة)..." "Magenta" "🧪"
Write-TimestampMessage "⏳ يرجى الانتظار... تم تطبيق الحماية الكاملة التلقائية..." "Yellow" "⏳"

if ($content -match "generateFinalComprehensiveReport\s*\(") {
    Add-TestResult "generateFinalComprehensiveReport exists" "PASS" "Main function found" "Core"
    
    # فحص محتوى الدالة للتأكد من عدم وجود استخدامات غير آمنة
    $funcMatch = [regex]::Match($content, "generateFinalComprehensiveReport\s*\([^{]*\{")
    if ($funcMatch.Success) {
        $funcStart = $funcMatch.Index + $funcMatch.Length
        $braceCount = 1
        $funcEnd = $funcStart
        
        for ($i = $funcStart; $i -lt $content.Length -and $braceCount -gt 0; $i++) {
            if ($content[$i] -eq '{') { $braceCount++ }
            elseif ($content[$i] -eq '}') { $braceCount-- }
            $funcEnd = $i
        }
        
        $funcContent = $content.Substring($funcStart, $funcEnd - $funcStart)
        $unsafeIncludes = [regex]::Matches($funcContent, "vulnType\.includes\(")
        
        if ($unsafeIncludes.Count -eq 0) {
            Add-TestResult "generateFinalComprehensiveReport safe" "PASS" "No unsafe includes usage" "Safety"
            Write-TimestampMessage "✅ دالة generateFinalComprehensiveReport آمنة تماماً" "Green" "✅"
        } else {
            Add-TestResult "generateFinalComprehensiveReport unsafe" "FAIL" "Found $($unsafeIncludes.Count) unsafe usages" "Safety"
            Write-TimestampMessage "❌ دالة generateFinalComprehensiveReport تحتوي على $($unsafeIncludes.Count) استخدامات غير آمنة" "Red" "❌"
        }
    }
} else {
    Add-TestResult "generateFinalComprehensiveReport missing" "FAIL" "Main function not found" "Core"
    Write-TimestampMessage "❌ دالة generateFinalComprehensiveReport مفقودة" "Red" "❌"
}

# 7. اختبار الضغط (إذا تم طلبه)
if ($StressTest) {
    Write-TimestampMessage "💪 تشغيل اختبار الضغط المتقدم..." "Magenta" "💪"
    
    # فحص جميع الدوال التي تستخدم vulnType
    $vulnTypeFunctions = [regex]::Matches($content, "function\s+\w*[^(]*\([^)]*vulnType[^)]*\)")
    Write-TimestampMessage "📊 عدد الدوال التي تستخدم vulnType: $($vulnTypeFunctions.Count)" "Cyan" "📊"
    
    if ($vulnTypeFunctions.Count -gt 0) {
        Add-TestResult "VulnType functions found" "PASS" "Found $($vulnTypeFunctions.Count) functions" "Stress"
    }
}

# 8. فحص معالجة الأخطاء
Write-TimestampMessage "🔧 فحص معالجة الأخطاء..." "Blue" "🔧"
$errorHandling = [regex]::Matches($content, "is not a function")
if ($errorHandling.Count -gt 0) {
    Add-TestResult "Error handling exists" "PASS" "Found error handling code" "Safety"
    Write-TimestampMessage "✅ معالجة أخطاء includes موجودة" "Green" "✅"
} else {
    Add-TestResult "Error handling missing" "WARN" "May need error handling" "Safety"
    Write-TimestampMessage "⚠️ معالجة أخطاء includes قد تكون مفقودة" "Yellow" "⚠️"
}

# 9. اختبار الخادم
Write-TimestampMessage "🌐 اختبار تشغيل الخادم..." "Cyan" "🌐"
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/ULTIMATE_PROTECTION_TEST.html" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Add-TestResult "Server working" "PASS" "Test page accessible" "Server"
        Write-TimestampMessage "✅ الخادم يعمل - صفحة الاختبار متاحة" "Green" "✅"
    } else {
        Add-TestResult "Server issue" "WARN" "Response code: $($response.StatusCode)" "Server"
        Write-TimestampMessage "⚠️ مشكلة في الخادم: كود $($response.StatusCode)" "Yellow" "⚠️"
    }
} catch {
    Add-TestResult "Server not working" "WARN" "Cannot reach server on port 3000" "Server"
    Write-TimestampMessage "⚠️ الخادم لا يعمل على المنفذ 3000" "Yellow" "⚠️"
}

# 10. النتائج النهائية
$elapsedTime = ((Get-Date) - $startTime)
Write-TimestampMessage "⏱️ الوقت المستغرق: $([math]::Round($elapsedTime.TotalSeconds)) ثانية" "Gray" "⏱️"

Write-Host ""
Write-Host "📊 ULTIMATE PROTECTION TEST RESULTS 📊" -ForegroundColor Cyan -BackgroundColor Black
Write-Host "=" * 80 -ForegroundColor Gray

# عرض الإحصائيات
Write-Host "📈 إحصائيات شاملة:" -ForegroundColor Yellow
Write-Host "   ✅ نجح: $($stats.Passed)" -ForegroundColor Green
Write-Host "   ❌ فشل: $($stats.Failed)" -ForegroundColor Red  
Write-Host "   ⚠️ تحذير: $($stats.Warnings)" -ForegroundColor Yellow
Write-Host "   📊 إجمالي: $($stats.Total)" -ForegroundColor Cyan
Write-Host "   📄 أسطر الكود: $(($content -split "`n").Count)" -ForegroundColor Gray
Write-Host "   🛡️ استخدامات آمنة: $($safeIncludesUsage.Count + $safeToLowerCaseUsage.Count)" -ForegroundColor Green

$successRate = if ($stats.Total -gt 0) { [math]::Round(($stats.Passed / $stats.Total) * 100, 1) } else { 0 }
Write-Host "   📈 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -gt 90) { "Green" } elseif ($successRate -gt 70) { "Yellow" } else { "Red" })

# النتيجة النهائية
if ($stats.Failed -eq 0) {
    Add-TestResult "ULTIMATE PROTECTION TEST" "PASS" "All critical tests passed! System fully protected" "Final"
    Show-VictoryBanner
} else {
    Add-TestResult "ULTIMATE PROTECTION TEST" "FAIL" "Found $($stats.Failed) critical issues" "Final"
    Show-FailureBanner
}

# عرض التفاصيل إذا تم طلبها
if ($Detailed) {
    Write-Host ""
    Write-Host "📋 تفاصيل جميع الاختبارات:" -ForegroundColor Yellow
    Write-Host "-" * 80 -ForegroundColor Gray
    
    $testResults | Group-Object Category | ForEach-Object {
        Write-Host "📁 $($_.Name):" -ForegroundColor Cyan
        $_.Group | ForEach-Object {
            $color = switch ($_.Status) {
                "PASS" { "Green" }
                "FAIL" { "Red" }
                "WARN" { "Yellow" }
                default { "White" }
            }
            Write-Host "   [$($_.Time)] $($_.Status): $($_.Test)" -ForegroundColor $color
            if ($_.Details) {
                Write-Host "      $($_.Details)" -ForegroundColor Gray
            }
        }
        Write-Host ""
    }
}

Write-Host "🏆 ULTIMATE PROTECTION TEST COMPLETED 🏆" -ForegroundColor Cyan -BackgroundColor Black
Write-Host "=" * 80 -ForegroundColor Gray
