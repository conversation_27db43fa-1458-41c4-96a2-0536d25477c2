<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار النظام المثابر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار النظام المثابر</h1>
            <p>تشخيص مشكلة vuln is not defined</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🔍 اختبار مباشر للنظام المثابر</h3>
                <p>سيتم اختبار النظام المثابر مباشرة لتحديد مصدر الخطأ</p>
                
                <button class="test-button" onclick="testPersistentSystemDirect()">
                    🔍 اختبار النظام المثابر مباشرة
                </button>
                
                <button class="test-button" onclick="testVulnerabilityObject()">
                    🧪 اختبار كائن الثغرة
                </button>
                
                <button class="test-button" onclick="testFunctionCalls()">
                    📞 اختبار استدعاءات الدوال
                </button>
                
                <div class="results" id="testResults">
جاهز للاختبار...

🎯 سيتم اختبار:
1. إنشاء كائن ثغرة وهمي
2. استدعاء النظام المثابر مباشرة
3. مراقبة الأخطاء والرسائل
4. تحديد مصدر خطأ vuln is not defined
                </div>
            </div>

            <div class="test-section">
                <h3>📊 تحليل الأخطاء</h3>
                <div class="results" id="errorAnalysis">
انتظار نتائج الاختبار...
                </div>
            </div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type;
            results.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            results.scrollTop = results.scrollHeight;
        }

        function addError(message) {
            const errorAnalysis = document.getElementById('errorAnalysis');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            errorAnalysis.innerHTML += `<span class="error">[${timestamp}] ${message}</span>\n`;
            errorAnalysis.scrollTop = errorAnalysis.scrollHeight;
        }

        async function testPersistentSystemDirect() {
            addResult('🔍 بدء اختبار النظام المثابر مباشرة...', 'info');
            
            try {
                // إنشاء instance من BugBountyCore
                const bugBounty = new BugBountyCore();
                addResult('✅ تم إنشاء instance من BugBountyCore', 'success');
                
                // إنشاء كائن ثغرة وهمي
                const testVulnerability = {
                    name: 'Test SQL Injection',
                    type: 'SQL Injection',
                    severity: 'High',
                    url: 'http://testphp.vulnweb.com/artists.php?artist=1'
                };
                addResult('✅ تم إنشاء كائن ثغرة وهمي', 'success');
                
                // اختبار النظام المثابر
                addResult('🔄 استدعاء النظام المثابر...', 'info');
                const result = await bugBounty.performPersistentVulnerabilityScanning(
                    'http://testphp.vulnweb.com', 
                    'Test prompt'
                );
                
                addResult(`✅ النظام المثابر اكتمل - النتائج: ${result.length} ثغرة`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في النظام المثابر: ${error.message}`, 'error');
                addError(`خطأ مفصل: ${error.stack}`);
                
                if (error.message.includes('vuln is not defined')) {
                    addError('🎯 تم تحديد المشكلة: vuln is not defined');
                    addError('المشكلة في إحدى الدوال التي تستخدم متغير vuln بدون تعريف');
                }
            }
        }

        async function testVulnerabilityObject() {
            addResult('🧪 اختبار كائن الثغرة...', 'info');
            
            try {
                const bugBounty = new BugBountyCore();
                
                const testVuln = {
                    name: 'Test XSS',
                    type: 'XSS',
                    severity: 'Medium'
                };
                
                addResult('🔄 اختبار applyPersistentScanningToVulnerability...', 'info');
                const result = await bugBounty.applyPersistentScanningToVulnerability(
                    testVuln, 
                    'http://testphp.vulnweb.com', 
                    'test prompt'
                );
                
                addResult('✅ applyPersistentScanningToVulnerability اكتمل', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في applyPersistentScanningToVulnerability: ${error.message}`, 'error');
                addError(`تفاصيل الخطأ: ${error.stack}`);
            }
        }

        async function testFunctionCalls() {
            addResult('📞 اختبار استدعاءات الدوال...', 'info');
            
            try {
                const bugBounty = new BugBountyCore();
                
                const testVuln = {
                    name: 'Test Function Calls',
                    type: 'SQL Injection',
                    severity: 'High'
                };
                
                // اختبار الدوال المساعدة واحدة تلو الأخرى
                addResult('🔄 اختبار findExploitationVariants...', 'info');
                const variants = await bugBounty.findExploitationVariants(testVuln, 'http://test.com');
                addResult(`✅ findExploitationVariants: ${variants.length} نتيجة`, 'success');
                
                addResult('🔄 اختبار findBypassTechniques...', 'info');
                const bypass = await bugBounty.findBypassTechniques(testVuln, 'http://test.com');
                addResult(`✅ findBypassTechniques: ${bypass.length} نتيجة`, 'success');
                
                addResult('🔄 اختبار analyzeRealImpact...', 'info');
                const impact = await bugBounty.analyzeRealImpact(testVuln, 'http://test.com');
                addResult(`✅ analyzeRealImpact: ${impact.enhanced_impact}`, 'success');
                
                addResult('🔄 اختبار discoverNewVulnerabilitiesFromPersistentScanning...', 'info');
                const discovered = await bugBounty.discoverNewVulnerabilitiesFromPersistentScanning(testVuln, 'http://test.com', 1);
                addResult(`✅ discoverNewVulnerabilitiesFromPersistentScanning: ${discovered.length} ثغرة`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار الدوال: ${error.message}`, 'error');
                addError(`تفاصيل الخطأ: ${error.stack}`);
                
                if (error.message.includes('vuln is not defined')) {
                    addError('🎯 المشكلة محددة: الخطأ في إحدى الدوال المساعدة');
                }
            }
        }

        // التقاط الأخطاء العامة
        window.addEventListener('error', function(event) {
            addError(`خطأ JavaScript عام: ${event.error.message}`);
            if (event.error.stack) {
                addError(`Stack trace: ${event.error.stack}`);
            }
        });
    </script>
</body>
</html>
