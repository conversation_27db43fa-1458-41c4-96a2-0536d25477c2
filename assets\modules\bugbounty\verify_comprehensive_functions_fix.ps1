# التحقق من إصلاح الدوال الشاملة التفصيلية في Bug Bounty v4.0
# تاريخ الإنشاء: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

Write-Host "🔥 بدء التحقق من إصلاح الدوال الشاملة التفصيلية - Bug Bounty v4.0" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Yellow

$coreFile = "assets/modules/bugbounty/BugBountyCore.js"
$testsPassed = 0
$testsFailed = 0
$totalTests = 0

function Test-Function {
    param(
        [string]$TestName,
        [string]$Pattern,
        [string]$File,
        [string]$ExpectedBehavior
    )
    
    $global:totalTests++
    Write-Host "`n🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "📋 السلوك المتوقع: $ExpectedBehavior" -ForegroundColor Gray
    
    try {
        $content = Get-Content $File -Raw -Encoding UTF8
        $matches = [regex]::Matches($content, $Pattern)
        
        if ($matches.Count -gt 0) {
            Write-Host "✅ نجح: تم العثور على $($matches.Count) تطابق" -ForegroundColor Green
            $global:testsPassed++
            return $true
        } else {
            Write-Host "❌ فشل: لم يتم العثور على التطابق المطلوب" -ForegroundColor Red
            $global:testsFailed++
            return $false
        }
    } catch {
        Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
        $global:testsFailed++
        return $false
    }
}

function Test-ContentQuality {
    param(
        [string]$TestName,
        [string]$Pattern,
        [string]$File,
        [string]$BadPattern
    )
    
    $global:totalTests++
    Write-Host "`n🔍 اختبار جودة المحتوى: $TestName" -ForegroundColor Yellow
    
    try {
        $content = Get-Content $File -Raw -Encoding UTF8
        $goodMatches = [regex]::Matches($content, $Pattern)
        $badMatches = [regex]::Matches($content, $BadPattern)
        
        if ($goodMatches.Count -gt 0 -and $badMatches.Count -eq 0) {
            Write-Host "✅ نجح: محتوى عالي الجودة ($($goodMatches.Count) تطابق جيد، $($badMatches.Count) تطابق سيء)" -ForegroundColor Green
            $global:testsPassed++
            return $true
        } else {
            Write-Host "❌ فشل: محتوى منخفض الجودة ($($goodMatches.Count) تطابق جيد، $($badMatches.Count) تطابق سيء)" -ForegroundColor Red
            $global:testsFailed++
            return $false
        }
    } catch {
        Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
        $global:testsFailed++
        return $false
    }
}

# التحقق من وجود الملف
if (-not (Test-Path $coreFile)) {
    Write-Host "❌ الملف غير موجود: $coreFile" -ForegroundColor Red
    exit 1
}

Write-Host "`n📁 الملف موجود: $coreFile" -ForegroundColor Green
$fileSize = (Get-Item $coreFile).Length
Write-Host "📏 حجم الملف: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Gray

# الاختبار 1: التحقق من استخدام الدوال الشاملة الحقيقية
Test-Function -TestName "استخدام الدوال الشاملة الحقيقية" `
    -Pattern "await this\.generateComprehensiveDetailsFromRealData" `
    -File $coreFile `
    -ExpectedBehavior "استخدام الدوال الشاملة مع await"

# الاختبار 2: التحقق من استخدام البيانات الحقيقية
Test-Function -TestName "استخراج البيانات الحقيقية" `
    -Pattern "extractRealDataFromDiscoveredVulnerability" `
    -File $coreFile `
    -ExpectedBehavior "استخراج البيانات الحقيقية من الثغرات المكتشفة"

# الاختبار 3: التحقق من إنشاء التأثير الديناميكي
Test-Function -TestName "التأثير الديناميكي" `
    -Pattern "generateDynamicImpactForAnyVulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء تأثير ديناميكي باستخدام البيانات الحقيقية"

# الاختبار 4: التحقق من الحوار التفاعلي
Test-Function -TestName "الحوار التفاعلي المفصل" `
    -Pattern "generateRealDetailedDialogueFromDiscoveredVulnerability" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء حوار تفاعلي مفصل من الثغرة المكتشفة"

# الاختبار 5: التحقق من التغيرات البصرية
Test-Function -TestName "التغيرات البصرية الحقيقية" `
    -Pattern "generateRealVisualChangesForVulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء تغيرات بصرية حقيقية"

# الاختبار 6: التحقق من النتائج المثابرة
Test-Function -TestName "النتائج المثابرة الحقيقية" `
    -Pattern "generateRealPersistentResultsForVulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء نتائج مثابرة حقيقية"

# الاختبار 7: التحقق من خطوات الاستغلال الشاملة
Test-Function -TestName "خطوات الاستغلال الشاملة" `
    -Pattern "generateRealExploitationStepsForVulnerabilityComprehensive" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء خطوات استغلال شاملة"

# الاختبار 8: التحقق من الأدلة الحقيقية
Test-Function -TestName "الأدلة الحقيقية من الاختبار" `
    -Pattern "extractRealEvidenceFromTesting.*realData" `
    -File $coreFile `
    -ExpectedBehavior "استخراج أدلة حقيقية من الاختبار"

# الاختبار 9: التحقق من التوصيات الديناميكية
Test-Function -TestName "التوصيات الديناميكية" `
    -Pattern "generateDynamicRecommendationsForVulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء توصيات ديناميكية"

# الاختبار 10: التحقق من تحليل الخبراء
Test-Function -TestName "تحليل الخبراء الديناميكي" `
    -Pattern "generateDynamicExpertAnalysisForVulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "إنشاء تحليل خبراء ديناميكي"

# اختبار جودة المحتوى: التحقق من عدم وجود نص عام
Test-ContentQuality -TestName "عدم وجود نص عام في التوصيف" `
    -Pattern "التفاصيل الشاملة الحقيقية" `
    -File $coreFile `
    -BadPattern "مستخرجة تلقائياً من البرومبت"

# اختبار جودة المحتوى: التحقق من عدم وجود تأثير عام
Test-ContentQuality -TestName "عدم وجود تأثير عام" `
    -Pattern "التأثير الديناميكي الحقيقي" `
    -File $coreFile `
    -BadPattern "تأثير متغير حسب السياق"

# اختبار جودة المحتوى: التحقق من وجود رسائل تأكيد
Test-Function -TestName "رسائل تأكيد الدوال الشاملة" `
    -Pattern "تم استخدام جميع الدوال الشاملة التفصيلية الحقيقية" `
    -File $coreFile `
    -ExpectedBehavior "وجود رسائل تأكيد استخدام الدوال الشاملة"

# اختبار التحقق من استخدام await في جميع الدوال
Test-Function -TestName "استخدام await في الدوال الشاملة" `
    -Pattern "await this\.generate.*Vulnerability.*realData" `
    -File $coreFile `
    -ExpectedBehavior "استخدام await مع البيانات الحقيقية"

# اختبار التحقق من إضافة الأقسام الجديدة
Test-Function -TestName "الأقسام الشاملة الجديدة" `
    -Pattern "التفاصيل الشاملة الحقيقية.*النظام v4\.0" `
    -File $coreFile `
    -ExpectedBehavior "وجود أقسام شاملة جديدة"

# عرض النتائج النهائية
Write-Host "`n" + "=" * 80 -ForegroundColor Yellow
Write-Host "📊 نتائج التحقق النهائية:" -ForegroundColor Cyan
Write-Host "✅ اختبارات ناجحة: $testsPassed" -ForegroundColor Green
Write-Host "❌ اختبارات فاشلة: $testsFailed" -ForegroundColor Red
Write-Host "📈 إجمالي الاختبارات: $totalTests" -ForegroundColor Gray

$successRate = if ($totalTests -gt 0) { [math]::Round(($testsPassed / $totalTests) * 100, 2) } else { 0 }
Write-Host "🎯 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($successRate -ge 90) {
    Write-Host "`n🎉 ممتاز! تم إصلاح الدوال الشاملة بنجاح" -ForegroundColor Green
    Write-Host "✅ النظام جاهز لإنتاج تقارير بتفاصيل شاملة حقيقية" -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "`n⚠️ جيد، لكن يحتاج بعض التحسينات" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ يحتاج المزيد من الإصلاحات" -ForegroundColor Red
}

Write-Host "`n📋 للاختبار الفعلي، قم بتشغيل:" -ForegroundColor Cyan
Write-Host "python -m http.server 3000" -ForegroundColor White
Write-Host "ثم افتح: http://localhost:3000/assets/modules/bugbounty/test_comprehensive_functions_fix.html" -ForegroundColor White

Write-Host "`n🔥 انتهى التحقق من إصلاح الدوال الشاملة التفصيلية" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Yellow
