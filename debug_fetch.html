<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Debug Fetch</title>
</head>
<body>
    <h1>Debug Fetch Test</h1>
    <button onclick="testFetch()">Test Fetch</button>
    <div id="results"></div>

    <script>
        function log(msg) {
            document.getElementById('results').innerHTML += '<div>' + msg + '</div>';
            console.log(msg);
        }

        async function testFetch() {
            log('Starting fetch test...');
            
            // Test 1: Simple file that should exist
            try {
                log('Test 1: Fetching index.html...');
                const response1 = await fetch('./index.html');
                log('Response 1: ' + response1.status + ' ' + response1.statusText);
            } catch (e) {
                log('Error 1: ' + e.message);
            }
            
            // Test 2: Template file
            try {
                log('Test 2: Fetching template...');
                const response2 = await fetch('./assets/modules/bugbounty/report_template.html');
                log('Response 2: ' + response2.status + ' ' + response2.statusText);
                if (response2.ok) {
                    const content = await response2.text();
                    log('Content length: ' + content.length);
                }
            } catch (e) {
                log('Error 2: ' + e.message);
            }
            
            // Test 3: Different path
            try {
                log('Test 3: Fetching with different path...');
                const response3 = await fetch('assets/modules/bugbounty/report_template.html');
                log('Response 3: ' + response3.status + ' ' + response3.statusText);
            } catch (e) {
                log('Error 3: ' + e.message);
            }
            
            // Test 4: Check current location
            log('Current location: ' + window.location.href);
            log('Current origin: ' + window.location.origin);
        }
    </script>
</body>
</html>
