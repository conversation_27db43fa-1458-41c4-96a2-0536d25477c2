<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Final Test - No Cache</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Final Test - تجنب التخزين المؤقت</h1>
    <button onclick="runFinalTest()">Run Final Test</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function runFinalTest() {
            document.getElementById('results').innerHTML = '';
            log('🔍 بدء الاختبار النهائي مع تجنب التخزين المؤقت...', 'info');
            
            try {
                // Clear any existing instances
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                    log('🗑️ تم حذف الكلاس القديم من الذاكرة', 'info');
                }
                
                // Remove existing script if any
                const existingScripts = document.querySelectorAll('script[src*="BugBountyCore"]');
                existingScripts.forEach(script => script.remove());
                
                // Force reload with cache busting
                const timestamp = new Date().getTime();
                const randomId = Math.random().toString(36).substring(7);
                const scriptUrl = `./assets/modules/bugbounty/BugBountyCore.js?v=${timestamp}&nocache=${randomId}&bust=${Date.now()}`;
                
                log(`📥 تحميل الملف مع تجنب التخزين المؤقت: ${scriptUrl}`, 'info');
                
                const script = document.createElement('script');
                script.src = scriptUrl;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف بنجاح', 'success');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل الملف', 'error');
                        reject(new Error('Failed to load script'));
                    };
                    document.head.appendChild(script);
                });
                
                // Wait for initialization
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Check class
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ الكلاس BugBountyCore غير موجود', 'error');
                    return;
                }
                log('✅ الكلاس BugBountyCore موجود', 'success');
                
                // Check prototype methods
                const prototypeMethods = Object.getOwnPropertyNames(BugBountyCore.prototype);
                log(`📋 عدد الدوال في prototype: ${prototypeMethods.length}`, 'info');
                
                const hasLoadMethod = prototypeMethods.includes('loadReportTemplate');
                const hasPreloadMethod = prototypeMethods.includes('preloadTemplate');
                
                log(`🔍 loadReportTemplate: ${hasLoadMethod ? '✅ موجود' : '❌ غير موجود'}`, hasLoadMethod ? 'success' : 'error');
                log(`🔍 preloadTemplate: ${hasPreloadMethod ? '⚠️ ما زال موجود' : '✅ تم حذفه'}`, hasPreloadMethod ? 'warning' : 'success');
                
                if (hasLoadMethod) {
                    log('🎉 النجاح! الدالة الجديدة موجودة', 'success');
                    
                    // Test creating instance
                    try {
                        log('🏗️ إنشاء مثيل من الكلاس...', 'info');
                        const core = new BugBountyCore();
                        log('✅ تم إنشاء المثيل بنجاح', 'success');
                        
                        if (typeof core.loadReportTemplate === 'function') {
                            log('✅ الدالة متاحة في المثيل', 'success');
                            
                            // Test the method
                            try {
                                log('🚀 اختبار دالة loadReportTemplate...', 'info');
                                await core.loadReportTemplate();
                                
                                if (core.reportTemplateHTML && core.reportTemplateHTML.length > 100) {
                                    log(`✅ تم تحميل القالب بنجاح! الحجم: ${core.reportTemplateHTML.length}`, 'success');
                                    log('🎉 الاختبار النهائي مكتمل بنجاح!', 'success');
                                    log('✅ النظام جاهز للاستخدام مع القالب الجديد', 'success');
                                } else {
                                    log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                                }
                            } catch (methodError) {
                                log(`❌ خطأ في استدعاء الدالة: ${methodError.message}`, 'error');
                            }
                        } else {
                            log('❌ الدالة غير متاحة في المثيل', 'error');
                        }
                        
                    } catch (constructorError) {
                        log(`❌ خطأ في إنشاء المثيل: ${constructorError.message}`, 'error');
                        console.error('تفاصيل الخطأ:', constructorError);
                    }
                } else {
                    log('❌ الدالة الجديدة غير موجودة', 'error');
                    log(`📋 أول 10 دوال: ${prototypeMethods.slice(0, 10).join(', ')}`, 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
