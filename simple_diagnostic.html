<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Simple Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .copy-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 10px 0;
        }
        .diagnostic-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Simple Diagnostic Test</h1>
    <button onclick="runSimpleDiagnostic()">Run Simple Diagnostic</button>
    <button class="copy-btn" onclick="copyDiagnostic()">Copy Diagnostic Results</button>
    
    <div id="results"></div>
    <div id="diagnosticOutput" class="diagnostic-output" style="display: none;"></div>

    <script>
        let diagnosticData = '';

        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            
            // إضافة للتشخيص
            diagnosticData += `[${new Date().toLocaleTimeString()}] [${type.toUpperCase()}] ${msg}\n`;
        }

        function addDiagnostic(category, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            let entry = `[${timestamp}] [${category}] ${message}`;
            if (data) {
                entry += ` | Data: ${JSON.stringify(data)}`;
            }
            diagnosticData += entry + '\n';
        }

        function copyDiagnostic() {
            const outputDiv = document.getElementById('diagnosticOutput');
            outputDiv.style.display = 'block';
            outputDiv.textContent = diagnosticData;
            
            // نسخ للحافظة
            navigator.clipboard.writeText(diagnosticData).then(() => {
                alert('تم نسخ التشخيص للحافظة!');
            }).catch(() => {
                // fallback للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = diagnosticData;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ التشخيص للحافظة!');
            });
        }

        async function runSimpleDiagnostic() {
            document.getElementById('results').innerHTML = '';
            diagnosticData = '';
            
            log('🔍 بدء التشخيص البسيط...', 'info');
            addDiagnostic('START', 'بدء التشخيص البسيط');
            
            try {
                // 1. تحميل BugBountyCore
                log('📥 تحميل BugBountyCore...', 'info');
                addDiagnostic('LOADING', 'بدء تحميل BugBountyCore');
                
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                    addDiagnostic('CLEANUP', 'تم حذف BugBountyCore القديم');
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore', 'success');
                addDiagnostic('LOADING', 'تم تحميل BugBountyCore بنجاح');
                
                // 2. إنشاء مثيل
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                addDiagnostic('INIT', 'تم إنشاء مثيل BugBountyCore');
                
                // 3. تحميل القالب
                log('📥 تحميل القالب...', 'info');
                addDiagnostic('TEMPLATE', 'بدء تحميل القالب');
                
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                if (!templateResponse.ok) {
                    throw new Error(`فشل تحميل القالب: ${templateResponse.status}`);
                }
                
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحميل القالب - الحجم: ${templateHTML.length} حرف`, 'success');
                addDiagnostic('TEMPLATE', 'تم تحميل القالب بنجاح', { size: templateHTML.length });
                
                // 4. اختبار الدالة مع تسجيل الأخطاء فقط
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                addDiagnostic('REPORT', 'بدء اختبار إنشاء التقرير');
                
                // تسجيل الأخطاء فقط
                const originalError = console.error;
                const errors = [];
                
                console.error = (...args) => {
                    const message = args.join(' ');
                    errors.push(message);
                    addDiagnostic('ERROR', message);
                    originalError(...args);
                };
                
                try {
                    const testData = {
                        total_vulnerabilities: 1,
                        vulnerabilities: [
                            { name: 'Test XSS', severity: 'High', description: 'Test vulnerability' }
                        ]
                    };
                    
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    
                    console.error = originalError;
                    
                    if (report && report.length > 1000) {
                        log(`✅ نجح! حجم التقرير: ${report.length} حرف`, 'success');
                        addDiagnostic('REPORT', 'تم إنشاء التقرير بنجاح', { size: report.length });
                        
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length}`, 'info');
                            addDiagnostic('VARIABLES', 'متغيرات غير مستبدلة', { count: variables.length, variables: variables.slice(0, 3) });
                        } else {
                            log('🎉 تم استبدال جميع المتغيرات!', 'success');
                            addDiagnostic('VARIABLES', 'تم استبدال جميع المتغيرات');
                        }
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                        addDiagnostic('REPORT', 'فشل في إنشاء التقرير', { 
                            reportExists: !!report,
                            reportLength: report ? report.length : 0
                        });
                    }
                    
                } catch (reportError) {
                    console.error = originalError;
                    
                    log(`❌ خطأ: ${reportError.message}`, 'error');
                    addDiagnostic('REPORT_ERROR', 'خطأ في إنشاء التقرير', {
                        message: reportError.message,
                        name: reportError.name,
                        stack: reportError.stack ? reportError.stack.split('\n').slice(0, 3).join('\n') : 'No stack'
                    });
                    
                    // عرض الأخطاء المسجلة
                    if (errors.length > 0) {
                        log(`📋 الأخطاء المسجلة: ${errors.length}`, 'info');
                        addDiagnostic('ERRORS_SUMMARY', 'ملخص الأخطاء', { count: errors.length });
                        
                        errors.slice(-5).forEach((error, index) => {
                            log(`   ${index + 1}. ${error.substring(0, 100)}...`, 'error');
                            addDiagnostic('ERROR_DETAIL', `خطأ ${index + 1}`, { message: error.substring(0, 200) });
                        });
                    }
                }
                
                addDiagnostic('COMPLETE', 'انتهى التشخيص');
                log('✅ انتهى التشخيص - اضغط "Copy Diagnostic Results" لنسخ النتائج', 'success');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                addDiagnostic('GENERAL_ERROR', 'خطأ عام', {
                    message: error.message,
                    stack: error.stack ? error.stack.split('\n').slice(0, 3).join('\n') : 'No stack'
                });
            }
        }
    </script>
</body>
</html>
