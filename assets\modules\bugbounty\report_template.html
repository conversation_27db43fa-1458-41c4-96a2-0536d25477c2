<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير Bug Bounty - {{TARGET_URL}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .section.summary {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-right: 5px solid #27ae60;
        }

        .section.vulnerabilities {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-right: 5px solid #e17055;
        }

        .section.impact {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            border-right: 5px solid #00b894;
        }

        .section.testing-details {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-right: 5px solid #ffc107;
        }

        .section.interactive-dialogues {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-right: 5px solid #dc3545;
        }

        .section.visual-changes {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-right: 5px solid #17a2b8;
        }

        .section.persistent-system {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            border-right: 5px solid #6c757d;
        }

        .section.recommendations {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-right: 5px solid #2d3436;
        }

        .section h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .section.recommendations h2 {
            color: white;
            border-bottom-color: rgba(255,255,255,0.3);
        }

        .vulnerability-item {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-right: 4px solid #e74c3c;
        }

        .vulnerability-item.critical {
            border-right-color: #e74c3c;
        }

        .vulnerability-item.high {
            border-right-color: #f39c12;
        }

        .vulnerability-item.medium {
            border-right-color: #f1c40f;
        }

        .vulnerability-item.low {
            border-right-color: #27ae60;
        }

        .vulnerability-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .vulnerability-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }

        .severity-badge {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 0.9em;
        }

        .severity-badge.critical {
            background: #e74c3c;
        }

        .severity-badge.high {
            background: #f39c12;
        }

        .severity-badge.medium {
            background: #f1c40f;
            color: #2c3e50;
        }

        .severity-badge.low {
            background: #27ae60;
        }

        .vulnerability-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .detail-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-right: 3px solid #3498db;
        }

        .detail-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .detail-value {
            color: #555;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .impact-visualization {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .impact-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }

        .before {
            background: #ffeaa7;
            border-right: 4px solid #fdcb6e;
        }

        .after {
            background: #fab1a0;
            border-right: 4px solid #e17055;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .footer .timestamp {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 10px;
        }

        .download-btn {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .vulnerability-details {
                grid-template-columns: 1fr;
            }
            
            .before-after {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .highlight {
            background: #f1c40f;
            padding: 2px 5px;
            border-radius: 3px;
            color: #2c3e50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ تقرير Bug Bounty الشامل</h1>
            <div class="subtitle">تحليل أمني متقدم بواسطة الذكاء الاصطناعي</div>
            <div class="subtitle">{{TARGET_URL}}</div>
        </div>

        <div class="content">
            <!-- ملخص التقييم -->
            <div class="section summary">
                <h2>📊 ملخص التقييم</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{TOTAL_VULNERABILITIES}}</div>
                        <div class="stat-label">إجمالي الثغرات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{SECURITY_LEVEL}}</div>
                        <div class="stat-label">مستوى الأمان</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{RISK_SCORE}}</div>
                        <div class="stat-label">نقاط المخاطر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{HIGHEST_SEVERITY}}</div>
                        <div class="stat-label">أعلى خطورة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{IMAGES_COUNT}}</div>
                        <div class="stat-label">الصور المدمجة</div>
                    </div>
                </div>
            </div>

            <!-- الثغرات المكتشفة مع التفاصيل الشاملة -->
            <div class="section vulnerabilities">
                <h2>🚨 الثغرات المكتشفة والمحللة بالكامل</h2>
                {{VULNERABILITIES_CONTENT}}
            </div>

            <!-- تفاصيل الاختبار والـ Payloads -->
            <div class="section testing-details">
                <h2>🔬 تفاصيل الاختبار والـ Payloads</h2>
                {{TESTING_DETAILS}}
            </div>

            <!-- الحوارات التفاعلية الشاملة -->
            <div class="section interactive-dialogues">
                <h2>💬 الحوارات التفاعلية الشاملة</h2>
                {{INTERACTIVE_DIALOGUES}}
            </div>

            <!-- التغيرات البصرية التفصيلية -->
            <div class="section visual-changes">
                <h2>🎨 التغيرات البصرية التفصيلية</h2>
                {{VISUAL_CHANGES}}
            </div>

            <!-- نتائج النظام المثابر -->
            <div class="section persistent-system">
                <h2>🔄 نتائج النظام المثابر</h2>
                {{PERSISTENT_RESULTS}}
            </div>

            <!-- صور التأثير والاستغلال -->
            <div class="section impact">
                <h2>📸 صور التأثير والاستغلال</h2>
                {{IMPACT_VISUALIZATIONS}}
            </div>

            <!-- التوصيات -->
            <div class="section recommendations">
                <h2>🔧 التوصيات والإصلاحات</h2>
                {{RECOMMENDATIONS_CONTENT}}
            </div>
        </div>

        <div class="footer">
            <div>
                <button class="download-btn" onclick="downloadReport()">📥 تحميل التقرير</button>
                <button class="download-btn" onclick="printReport()">🖨️ طباعة التقرير</button>
            </div>
            <div class="timestamp">
                تم إنشاء التقرير في: {{TIMESTAMP}}<br>
                بواسطة: نظام Bug Bounty المتقدم v3.0
            </div>
        </div>
    </div>

    <script>
        function downloadReport() {
            const element = document.documentElement;
            const opt = {
                margin: 1,
                filename: 'bug-bounty-report-{{DATE}}.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
            };
            
            // استخدام html2pdf إذا كان متاحاً
            if (typeof html2pdf !== 'undefined') {
                html2pdf().set(opt).from(element).save();
            } else {
                // تحميل كـ HTML
                const blob = new Blob([document.documentElement.outerHTML], {
                    type: 'text/html;charset=utf-8'
                });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'bug-bounty-report-{{DATE}}.html';
                link.click();
            }
        }

        function printReport() {
            window.print();
        }

        // تحسين العرض عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(20px)';
                    section.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
