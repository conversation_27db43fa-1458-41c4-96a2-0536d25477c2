# 🚀 TOTAL PROTECTION TEST - PowerShell Verification 🚀
# اختبار شامل للتحقق من إصلاح جميع استخدامات includes في Bug Bounty v4.0

Write-Host "🚀 TOTAL PROTECTION TEST - PowerShell Verification 🚀" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

$startTime = Get-Date
$testResults = @()

# دالة لإضافة نتيجة اختبار
function Add-TestResult {
    param($TestName, $Status, $Details)
    $global:testResults += [PSCustomObject]@{
        Test = $TestName
        Status = $Status
        Details = $Details
        Time = (Get-Date).ToString("HH:mm:ss")
    }
}

# دالة لعرض النتائج
function Show-Results {
    Write-Host "`nTest Results:" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray

    $passed = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failed = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $warnings = ($testResults | Where-Object { $_.Status -eq "WARN" }).Count

    foreach ($result in $testResults) {
        $color = switch ($result.Status) {
            "PASS" { "Green" }
            "FAIL" { "Red" }
            "WARN" { "Yellow" }
            default { "White" }
        }
        Write-Host "[$($result.Time)] $($result.Status): $($result.Test)" -ForegroundColor $color
        if ($result.Details) {
            Write-Host "    $($result.Details)" -ForegroundColor Gray
        }
    }

    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "PASSED: $passed" -ForegroundColor Green
    Write-Host "FAILED: $failed" -ForegroundColor Red
    Write-Host "WARNINGS: $warnings" -ForegroundColor Yellow
    Write-Host "Total Time: $((Get-Date) - $startTime)" -ForegroundColor Gray
}

# 1. Check core file exists
Write-Host "Checking BugBountyCore.js file..." -ForegroundColor Yellow
$coreFile = ".\BugBountyCore.js"
if (Test-Path $coreFile) {
    Add-TestResult "BugBountyCore.js exists" "PASS" "File found and accessible"
} else {
    Add-TestResult "BugBountyCore.js missing" "FAIL" "Cannot find core file"
    Show-Results
    exit 1
}

# 2. Check direct includes usage
Write-Host "Checking direct includes usage..." -ForegroundColor Yellow
$content = Get-Content $coreFile -Raw -Encoding UTF8
$directIncludes = [regex]::Matches($content, "vulnType\.includes\(|safeVulnType\.includes\(")

if ($directIncludes.Count -eq 0) {
    Add-TestResult "No direct includes usage" "PASS" "All direct usage fixed"
} else {
    Add-TestResult "Direct includes found" "FAIL" "Direct usage count: $($directIncludes.Count)"

    # Show direct usage
    Write-Host "Direct includes found:" -ForegroundColor Red
    $lines = $content -split "`n"
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match "vulnType\.includes\(|safeVulnType\.includes\(") {
            Write-Host "   Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Red
        }
    }
}

# 3. Check safe functions exist
Write-Host "Checking safe functions..." -ForegroundColor Yellow

$safeFunctions = @(
    "safeToLowerCase",
    "safeIncludes",
    "createSafeStringObject",
    "makeSafeVulnType",
    "getSafeVulnType"
)

foreach ($func in $safeFunctions) {
    if ($content -match "$func\s*\(") {
        Add-TestResult "Function $func exists" "PASS" "Function found in code"
    } else {
        Add-TestResult "Function $func missing" "FAIL" "Function not found"
    }
}

# 4. Check safe function usage
Write-Host "Checking safe function usage..." -ForegroundColor Yellow

$safeUsages = [regex]::Matches($content, "this\.safeIncludes\(")
if ($safeUsages.Count -gt 50) {
    Add-TestResult "safeIncludes usage" "PASS" "Usage count: $($safeUsages.Count)"
} else {
    Add-TestResult "safeIncludes usage low" "WARN" "Usage count: $($safeUsages.Count) - may need more"
}

# 5. Check generateFinalComprehensiveReport function
Write-Host "Checking generateFinalComprehensiveReport function..." -ForegroundColor Yellow
if ($content -match "generateFinalComprehensiveReport\s*\(") {
    Add-TestResult "generateFinalComprehensiveReport exists" "PASS" "Main function found"

    # Check function content
    $funcMatch = [regex]::Match($content, "generateFinalComprehensiveReport\s*\([^{]*\{")
    if ($funcMatch.Success) {
        $funcStart = $funcMatch.Index + $funcMatch.Length
        $braceCount = 1
        $funcEnd = $funcStart

        for ($i = $funcStart; $i -lt $content.Length -and $braceCount -gt 0; $i++) {
            if ($content[$i] -eq '{') { $braceCount++ }
            elseif ($content[$i] -eq '}') { $braceCount-- }
            $funcEnd = $i
        }

        $funcContent = $content.Substring($funcStart, $funcEnd - $funcStart)
        $unsafeIncludes = [regex]::Matches($funcContent, "\.includes\(")

        if ($unsafeIncludes.Count -eq 0) {
            Add-TestResult "generateFinalComprehensiveReport safe" "PASS" "No unsafe includes usage"
        } else {
            Add-TestResult "generateFinalComprehensiveReport unsafe" "FAIL" "Found $($unsafeIncludes.Count) unsafe usages"
        }
    }
} else {
    Add-TestResult "generateFinalComprehensiveReport missing" "FAIL" "Main function not found"
}

# 6. Check error handling
Write-Host "Checking error handling..." -ForegroundColor Yellow
$errorHandling = [regex]::Matches($content, "is not a function")
if ($errorHandling.Count -gt 0) {
    Add-TestResult "Error handling exists" "PASS" "Found error handling code"
} else {
    Add-TestResult "Error handling missing" "WARN" "May need error handling"
}

# 7. Test server
Write-Host "Testing server..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/test_total_protection_fix.html" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Add-TestResult "Server working" "PASS" "Test page accessible"
    } else {
        Add-TestResult "Server issue" "WARN" "Response code: $($response.StatusCode)"
    }
} catch {
    Add-TestResult "Server not working" "WARN" "Cannot reach server on port 3000"
}

# 8. Check template file
Write-Host "Checking template file..." -ForegroundColor Yellow
$templateFile = ".\templates\report_template.html"
if (Test-Path $templateFile) {
    Add-TestResult "Template file exists" "PASS" "report_template.html found"
} else {
    Add-TestResult "Template file missing" "WARN" "May affect report generation"
}

# 9. Code statistics
Write-Host "Code statistics..." -ForegroundColor Yellow
$lines = ($content -split "`n").Count
$safeIncludesCount = ([regex]::Matches($content, "this\.safeIncludes")).Count
$safeToLowerCaseCount = ([regex]::Matches($content, "this\.safeToLowerCase")).Count

Add-TestResult "Code statistics" "PASS" "Lines: $lines, safeIncludes: $safeIncludesCount, safeToLowerCase: $safeToLowerCaseCount"

# 10. Final comprehensive test
Write-Host "Final comprehensive test..." -ForegroundColor Yellow
$criticalIssues = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
if ($criticalIssues -eq 0) {
    Add-TestResult "TOTAL PROTECTION TEST" "PASS" "All tests passed! System fully protected"
    Write-Host "`nTOTAL PROTECTION TEST PASSED!" -ForegroundColor Green
} else {
    Add-TestResult "TOTAL PROTECTION TEST" "FAIL" "Found $criticalIssues critical issues"
    Write-Host "`nTOTAL PROTECTION TEST FAILED - Issues need fixing" -ForegroundColor Red
}

# Show final results
Show-Results

Write-Host "`nTOTAL PROTECTION TEST COMPLETED" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Gray
