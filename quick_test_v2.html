<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Quick Test v2</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #2c3e50; 
            color: white; 
        }
        .log { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            border-left: 4px solid; 
        }
        .success { background: #27ae60; border-color: #2ecc71; }
        .error { background: #e74c3c; border-color: #c0392b; }
        .info { background: #3498db; border-color: #2980b9; }
        button { 
            background: #e74c3c; 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px 0;
        }
        button:hover { background: #c0392b; }
    </style>
</head>
<body>
    <h1>🧪 Quick Test v2</h1>
    <button onclick="runQuickTest()">🚀 Run Quick Test v2</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${msg}`;
            document.getElementById('results').appendChild(div);
        }

        async function runQuickTest() {
            document.getElementById('results').innerHTML = '';
            
            log('🚀 بدء الاختبار السريع v2...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const core = new BugBountyCore();
                
                log('✅ تم تحميل BugBountyCore بنجاح', 'success');
                
                // اختبار دالة safeToLowerCase
                const testResult = core.safeToLowerCase('XSS Test');
                log(`🧪 اختبار safeToLowerCase: ${testResult}`, 'info');
                
                // اختبار includes
                if (testResult.includes && typeof testResult.includes === 'function') {
                    const includesResult = testResult.includes('xss');
                    log(`✅ دالة includes تعمل: ${includesResult}`, 'success');
                } else {
                    log('❌ دالة includes لا تعمل', 'error');
                }
                
                // اختبار بسيط للثغرة
                const testVuln = { name: 'XSS Test', type: 'xss', severity: 'High' };
                const vulnType = core.safeToLowerCase(testVuln.name);
                
                if (vulnType.includes('xss')) {
                    log('✅ اختبار vulnType.includes نجح!', 'success');
                } else {
                    log('❌ اختبار vulnType.includes فشل!', 'error');
                }
                
                // اختبار generateFinalComprehensiveReport
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                const testData = {
                    vulnerabilities: [testVuln]
                };
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    
                    if (report && report.length > 100) {
                        log(`✅ تم إنشاء التقرير بنجاح! الحجم: ${report.length} حرف`, 'success');
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ في التقرير: ${reportError.message}`, 'error');
                    
                    if (reportError.message.includes('includes')) {
                        log('❌ ما زالت مشكلة includes موجودة!', 'error');
                    }
                }
                
                log('🎉 الاختبار السريع v2 مكتمل!', 'success');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                
                if (error.message.includes('includes')) {
                    log('❌ ما زالت مشكلة includes موجودة!', 'error');
                } else {
                    log('ℹ️ نوع خطأ مختلف', 'info');
                }
            }
        }
    </script>
</body>
</html>
