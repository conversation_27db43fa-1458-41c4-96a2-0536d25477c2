// اختبار الدوال الشاملة التفصيلية في النظام v4

console.log('🔍 اختبار الدوال الشاملة التفصيلية...');

// تحميل النظام
let bugBountyCore;

async function testComprehensiveFunctions() {
    try {
        // تحميل النظام
        if (typeof BugBountyCore !== 'undefined') {
            bugBountyCore = new BugBountyCore();
            console.log('✅ تم تحميل BugBountyCore بنجاح');
        } else {
            console.log('❌ BugBountyCore غير متوفر');
            return;
        }

        // إنشاء ثغرة اختبار مع تفاصيل شاملة
        const testVuln = {
            name: 'SQL Injection in Login Form',
            type: 'sql injection',
            severity: 'High',
            description: 'SQL injection vulnerability discovered in login form',
            url: 'http://testphp.vulnweb.com/login.php',
            parameter: 'username',
            method: 'POST',
            tested_payload: "' OR '1'='1'-- -",
            evidence: 'MySQL error message revealed',
            response_code: 200,
            response_time: 1500,
            discovery_method: 'Automated scanning',
            discovery_timestamp: new Date().toISOString(),
            exploitation_result: 'Successfully bypassed authentication',
            testing_results: {
                confirmed: true,
                impact: 'High',
                exploitable: true
            }
        };

        console.log('🧪 اختبار الثغرة:', testVuln);

        // اختبار 1: استخراج البيانات الحقيقية
        console.log('\n🔍 اختبار استخراج البيانات الحقيقية...');
        const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
        console.log('📊 البيانات المستخرجة:', realData);

        // اختبار 2: إنشاء خطوات الاستغلال الشاملة
        console.log('\n🧪 اختبار إنشاء خطوات الاستغلال الشاملة...');
        const exploitationSteps = bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
        console.log('📋 خطوات الاستغلال:', exploitationSteps);

        // اختبار 3: إنشاء الحوار التفاعلي
        console.log('\n💬 اختبار إنشاء الحوار التفاعلي...');
        const dialogue = bugBountyCore.generateInteractiveDialogue(testVuln, realData);
        console.log('🗣️ الحوار التفاعلي:', dialogue);

        // اختبار 4: إنشاء التغيرات البصرية
        console.log('\n🎨 اختبار إنشاء التغيرات البصرية...');
        const visualChanges = bugBountyCore.generateRealVisualChangesForVulnerability(testVuln);
        console.log('👁️ التغيرات البصرية:', visualChanges);

        // اختبار 5: إنشاء النتائج المثابرة
        console.log('\n🔄 اختبار إنشاء النتائج المثابرة...');
        const persistentResults = bugBountyCore.generateRealPersistentResultsForVulnerability(testVuln, realData);
        console.log('📈 النتائج المثابرة:', persistentResults);

        // اختبار 6: إنشاء التوصيات الديناميكية
        console.log('\n📝 اختبار إنشاء التوصيات الديناميكية...');
        const recommendations = bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln);
        console.log('💡 التوصيات:', recommendations);

        // اختبار 7: إنشاء تحليل الخبراء
        console.log('\n🎓 اختبار إنشاء تحليل الخبراء...');
        const expertAnalysis = bugBountyCore.generateDynamicExpertAnalysisForVulnerability(testVuln);
        console.log('🔬 تحليل الخبراء:', expertAnalysis);

        // اختبار 8: إنشاء القسم الشامل للثغرة
        console.log('\n📄 اختبار إنشاء القسم الشامل للثغرة...');
        const comprehensiveSection = await bugBountyCore.formatComprehensiveVulnerabilitySection(testVuln, 1, 'http://testphp.vulnweb.com');
        console.log('📋 القسم الشامل (أول 500 حرف):', comprehensiveSection.substring(0, 500) + '...');

        // اختبار 9: إنشاء التقرير النهائي
        console.log('\n📊 اختبار إنشاء التقرير النهائي...');
        const testAnalysis = {
            vulnerabilities: [testVuln],
            summary: {
                total_vulnerabilities: 1,
                high_severity: 1,
                medium_severity: 0,
                low_severity: 0
            }
        };

        const testPages = [
            {
                url: 'http://testphp.vulnweb.com',
                vulnerabilities: [testVuln]
            }
        ];

        try {
            const finalReport = await bugBountyCore.generateFinalComprehensiveReport(
                testAnalysis, 
                testPages, 
                'http://testphp.vulnweb.com'
            );
            
            console.log('📄 التقرير النهائي تم إنشاؤه بنجاح!');
            console.log('📏 حجم التقرير:', finalReport.length, 'حرف');
            
            // البحث عن المحتوى التفصيلي في التقرير
            const hasExploitationSteps = finalReport.includes('خطوات الاستغلال');
            const hasDialogue = finalReport.includes('الحوار التفاعلي') || finalReport.includes('التحليل الأمني');
            const hasVisualChanges = finalReport.includes('التغيرات البصرية');
            const hasRecommendations = finalReport.includes('التوصيات');
            const hasExpertAnalysis = finalReport.includes('تحليل الخبراء');
            
            console.log('🔍 تحليل محتوى التقرير:');
            console.log('  📋 خطوات الاستغلال:', hasExploitationSteps ? '✅ موجودة' : '❌ مفقودة');
            console.log('  💬 الحوار التفاعلي:', hasDialogue ? '✅ موجود' : '❌ مفقود');
            console.log('  🎨 التغيرات البصرية:', hasVisualChanges ? '✅ موجودة' : '❌ مفقودة');
            console.log('  💡 التوصيات:', hasRecommendations ? '✅ موجودة' : '❌ مفقودة');
            console.log('  🔬 تحليل الخبراء:', hasExpertAnalysis ? '✅ موجود' : '❌ مفقود');
            
            if (hasExploitationSteps && hasDialogue && hasVisualChanges && hasRecommendations && hasExpertAnalysis) {
                console.log('\n🎉 جميع الدوال الشاملة التفصيلية تعمل بنجاح!');
                console.log('✅ التقرير يحتوي على جميع التفاصيل المطلوبة');
            } else {
                console.log('\n⚠️ بعض الدوال الشاملة التفصيلية لا تعمل كما هو متوقع');
                console.log('❌ التقرير لا يحتوي على جميع التفاصيل المطلوبة');
            }
            
        } catch (error) {
            console.log('❌ خطأ في إنشاء التقرير النهائي:', error.message);
        }

        console.log('\n🏆 اكتمل اختبار الدوال الشاملة التفصيلية!');

    } catch (error) {
        console.log('❌ خطأ في الاختبار:', error.message);
    }
}

// تشغيل الاختبار
if (typeof window !== 'undefined') {
    // في المتصفح
    window.testComprehensiveFunctions = testComprehensiveFunctions;
    console.log('🚀 اختبار جاهز! استخدم testComprehensiveFunctions() لتشغيل الاختبار');
} else {
    // في Node.js
    testComprehensiveFunctions();
}
