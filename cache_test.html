<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Cache Test</title>
</head>
<body>
    <h1>Cache Test</h1>
    <button onclick="testWithoutCache()">Test Without Cache</button>
    <div id="results"></div>

    <script>
        function log(msg) {
            document.getElementById('results').innerHTML += '<div>' + msg + '</div>';
            console.log(msg);
        }

        async function testWithoutCache() {
            document.getElementById('results').innerHTML = '';
            log('🔍 اختبار بدون تخزين مؤقت...');
            
            try {
                // Remove any existing script
                const existingScript = document.querySelector('script[src*="BugBountyCore"]');
                if (existingScript) {
                    existingScript.remove();
                    log('🗑️ تم حذف الملف القديم');
                }
                
                // Clear any existing class
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                    log('🗑️ تم حذف الكلاس القديم');
                }
                
                // Load fresh script with cache busting
                const timestamp = new Date().getTime();
                const randomId = Math.random().toString(36).substring(7);
                const scriptUrl = `./assets/modules/bugbounty/BugBountyCore.js?v=${timestamp}&nocache=${randomId}&fresh=true`;
                
                log(`📥 تحميل الملف الجديد: ${scriptUrl}`);
                
                const script = document.createElement('script');
                script.src = scriptUrl;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ تم تحميل الملف بنجاح');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ فشل في تحميل الملف');
                        reject(new Error('Failed to load script'));
                    };
                    document.head.appendChild(script);
                });
                
                // Wait for initialization
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Check class
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ الكلاس غير موجود');
                    return;
                }
                
                log('✅ الكلاس موجود');
                
                // Check prototype
                const prototypeMethods = Object.getOwnPropertyNames(BugBountyCore.prototype);
                log(`📋 عدد الدوال: ${prototypeMethods.length}`);
                
                const hasLoadMethod = prototypeMethods.includes('loadReportTemplate');
                const hasPreloadMethod = prototypeMethods.includes('preloadTemplate');
                
                log(`🔍 loadReportTemplate: ${hasLoadMethod ? '✅ موجود' : '❌ غير موجود'}`);
                log(`🔍 preloadTemplate: ${hasPreloadMethod ? '⚠️ ما زال موجود' : '✅ تم حذفه'}`);
                
                if (hasLoadMethod) {
                    log('🎉 النجاح! الدالة الجديدة موجودة');
                    
                    // Test creating instance
                    try {
                        const core = new BugBountyCore();
                        log('✅ تم إنشاء المثيل بنجاح');
                        
                        if (typeof core.loadReportTemplate === 'function') {
                            log('✅ الدالة متاحة في المثيل');
                        } else {
                            log('❌ الدالة غير متاحة في المثيل');
                        }
                    } catch (error) {
                        log(`❌ خطأ في إنشاء المثيل: ${error.message}`);
                    }
                } else {
                    log('❌ الدالة الجديدة غير موجودة');
                    log(`📋 أول 10 دوال: ${prototypeMethods.slice(0, 10).join(', ')}`);
                }
                
            } catch (error) {
                log(`❌ خطأ: ${error.message}`);
            }
        }
    </script>
</body>
</html>
