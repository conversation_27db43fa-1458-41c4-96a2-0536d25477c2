<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Simple Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Simple Debug</h1>
    <button onclick="simpleDebug()">Simple Debug</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function simpleDebug() {
            document.getElementById('results').innerHTML = '';
            log('🔍 بدء التشخيص البسيط...', 'info');
            
            try {
                // 1. تحميل القالب مباشرة
                log('📥 تحميل القالب مباشرة...', 'info');
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                
                if (!templateResponse.ok) {
                    throw new Error(`فشل تحميل القالب: ${templateResponse.status}`);
                }
                
                const templateHTML = await templateResponse.text();
                log(`✅ تم تحميل القالب - الحجم: ${templateHTML.length} حرف`, 'success');
                
                // 2. تحميل BugBountyCore
                log('📥 تحميل BugBountyCore...', 'info');
                
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore', 'success');
                
                // 3. إنشاء مثيل وحفظ القالب
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // حفظ القالب مباشرة
                core.reportTemplateHTML = templateHTML;
                log('✅ تم حفظ القالب في المثيل', 'success');
                
                // 4. فحص القالب في المثيل
                log(`🔍 فحص القالب في المثيل:`, 'info');
                log(`   - موجود: ${core.reportTemplateHTML ? 'نعم' : 'لا'}`, core.reportTemplateHTML ? 'success' : 'error');
                log(`   - الحجم: ${core.reportTemplateHTML ? core.reportTemplateHTML.length : 0} حرف`, 'info');
                log(`   - يحتوي على متغيرات: ${core.reportTemplateHTML && core.reportTemplateHTML.includes('{{') ? 'نعم' : 'لا'}`, 'info');
                
                // 5. اختبار استبدال بسيط
                log('🧪 اختبار استبدال بسيط...', 'info');
                
                if (core.reportTemplateHTML) {
                    let testReport = core.reportTemplateHTML;
                    
                    // استبدال بسيط
                    testReport = testReport
                        .replace(/{{TARGET_URL}}/g, 'https://test.com')
                        .replace(/{{TOTAL_VULNERABILITIES}}/g, '5')
                        .replace(/{{SECURITY_LEVEL}}/g, 'متوسط')
                        .replace(/{{TIMESTAMP}}/g, new Date().toLocaleString('ar'));
                    
                    // فحص النتيجة
                    const remainingVars = testReport.match(/{{[^}]+}}/g);
                    
                    if (remainingVars) {
                        log(`⚠️ متغيرات متبقية: ${remainingVars.length}`, 'info');
                        log(`   المتغيرات: ${remainingVars.slice(0, 5).join(', ')}`, 'info');
                    } else {
                        log('✅ تم استبدال جميع المتغيرات الأساسية', 'success');
                    }
                    
                    log(`📊 حجم التقرير المعالج: ${testReport.length} حرف`, 'success');
                    
                    // إنشاء رابط تحميل للاختبار
                    const blob = new Blob([testReport], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'test-simple-report.html';
                    link.textContent = 'تحميل التقرير التجريبي';
                    link.style.display = 'block';
                    link.style.margin = '10px 0';
                    link.style.padding = '10px';
                    link.style.background = '#007bff';
                    link.style.color = 'white';
                    link.style.textDecoration = 'none';
                    link.style.borderRadius = '5px';
                    
                    document.getElementById('results').appendChild(link);
                    
                    log('✅ تم إنشاء رابط التحميل', 'success');
                    
                } else {
                    log('❌ القالب غير متوفر للاختبار', 'error');
                }
                
                // 6. اختبار دالة generateFinalComprehensiveReport مع تسجيل مفصل
                log('🧪 اختبار generateFinalComprehensiveReport مع تسجيل مفصل...', 'info');
                
                // إضافة تسجيل مؤقت للدالة
                const originalConsoleLog = console.log;
                const logs = [];
                
                console.log = function(...args) {
                    logs.push(args.join(' '));
                    originalConsoleLog.apply(console, args);
                };
                
                try {
                    const testData = {
                        total_vulnerabilities: 2,
                        vulnerabilities: [
                            { name: 'XSS', severity: 'High' },
                            { name: 'SQL', severity: 'Critical' }
                        ]
                    };
                    
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://example.com');
                    
                    // استعادة console.log
                    console.log = originalConsoleLog;
                    
                    if (report && report.length > 1000) {
                        log(`✅ تم إنشاء التقرير - الحجم: ${report.length} حرف`, 'success');
                        
                        const hasVars = report.includes('{{');
                        log(`📋 يحتوي على متغيرات: ${hasVars ? 'نعم' : 'لا'}`, hasVars ? 'error' : 'success');
                        
                        if (hasVars) {
                            const vars = report.match(/{{[^}]+}}/g);
                            log(`   المتغيرات المتبقية: ${vars ? vars.slice(0, 3).join(', ') : 'غير معروف'}`, 'error');
                        }
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                        
                        // عرض آخر 10 سجلات من console.log
                        log('📋 آخر سجلات console.log:', 'info');
                        logs.slice(-10).forEach(logEntry => {
                            log(`   ${logEntry}`, 'info');
                        });
                    }
                    
                } catch (reportError) {
                    console.log = originalConsoleLog;
                    log(`❌ خطأ في التقرير: ${reportError.message}`, 'error');
                    
                    // عرض آخر سجلات
                    log('📋 آخر سجلات قبل الخطأ:', 'info');
                    logs.slice(-5).forEach(logEntry => {
                        log(`   ${logEntry}`, 'info');
                    });
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
