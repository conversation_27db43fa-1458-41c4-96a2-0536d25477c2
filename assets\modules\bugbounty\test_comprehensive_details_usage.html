<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استخدام التفاصيل الشاملة في التقارير</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-button {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9);
            width: 0%;
            transition: width 0.3s ease;
        }
        .details-preview {
            background: #2d3436;
            color: #ddd;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار استخدام التفاصيل الشاملة في التقارير</h1>
            <p>التحقق من أن التقارير تستخدم generateComprehensiveDetailsFromRealData</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🎯 الهدف من الاختبار</h3>
                <p>التحقق من أن التقارير (الرئيسي والمنفصلة) تستخدم الدوال الشاملة التفصيلية بدلاً من المحتوى العام</p>
                <ul>
                    <li>✅ استخدام generateComprehensiveDetailsFromRealData</li>
                    <li>✅ استخدام comprehensive_details في الوصف والتأثير</li>
                    <li>✅ استخدام التفاصيل الشاملة في جميع أجزاء التقرير</li>
                    <li>✅ عدم وجود محتوى عام مثل "تأثير متغير حسب السياق"</li>
                </ul>
            </div>

            <div class="test-section">
                <h3>🧪 اختبارات التفاصيل الشاملة</h3>
                <button class="test-button" onclick="testComprehensiveDetailsGeneration()">
                    🔬 اختبار إنشاء التفاصيل الشاملة
                </button>
                <button class="test-button" onclick="testReportUsage()">
                    📄 اختبار استخدام التقارير للتفاصيل الشاملة
                </button>
                <button class="test-button" onclick="testVulnerabilitySection()">
                    🚨 اختبار قسم الثغرة الشامل
                </button>
                <button class="test-button" onclick="runFullTest()">
                    🚀 تشغيل الاختبار الكامل
                </button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>

            <div id="testResults"></div>
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = [];

        // تهيئة النظام
        async function initializeSystem() {
            try {
                bugBountyCore = new BugBountyCore();
                addResult('✅ تم تهيئة BugBountyCore بنجاح', 'success');
                return true;
            } catch (error) {
                addResult(`❌ فشل في تهيئة BugBountyCore: ${error.message}`, 'error');
                return false;
            }
        }

        // إضافة نتيجة الاختبار
        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            document.getElementById('testResults').appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // تحديث شريط التقدم
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        // إنشاء ثغرة اختبارية
        function createTestVulnerability() {
            return {
                name: 'SQL Injection Test',
                type: 'SQL Injection',
                severity: 'High',
                location: 'http://testphp.vulnweb.com/artists.php?artist=1',
                parameter: 'artist',
                payload: "' OR '1'='1' --",
                method: 'GET',
                tested_payload: "' OR '1'='1' --",
                response: 'MySQL error: You have an error in your SQL syntax',
                evidence: 'SQL injection confirmed through error message',
                exploitation_result: {
                    success: true,
                    data_accessed: true,
                    impact: 'Database access achieved'
                }
            };
        }

        // اختبار إنشاء التفاصيل الشاملة
        async function testComprehensiveDetailsGeneration() {
            addResult('🔬 بدء اختبار إنشاء التفاصيل الشاملة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            try {
                const testVuln = createTestVulnerability();
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                
                addResult('📊 تم استخراج البيانات الحقيقية', 'success');
                addResult(`   البيانات: ${JSON.stringify(realData).substring(0, 100)}...`, 'info');

                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                if (comprehensiveDetails && typeof comprehensiveDetails === 'object') {
                    addResult('✅ تم إنشاء التفاصيل الشاملة بنجاح', 'success');
                    addResult(`   الأقسام: ${Object.keys(comprehensiveDetails).join(', ')}`, 'info');
                    
                    // عرض عينة من التفاصيل
                    const preview = document.createElement('div');
                    preview.className = 'details-preview';
                    preview.textContent = JSON.stringify(comprehensiveDetails, null, 2).substring(0, 1000) + '...';
                    document.getElementById('testResults').appendChild(preview);
                    
                    return true;
                } else {
                    addResult('❌ فشل في إنشاء التفاصيل الشاملة', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار التفاصيل الشاملة: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار استخدام التقارير للتفاصيل الشاملة
        async function testReportUsage() {
            addResult('📄 بدء اختبار استخدام التقارير للتفاصيل الشاملة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            try {
                const testVuln = createTestVulnerability();
                
                // إضافة التفاصيل الشاملة للثغرة
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                // اختبار formatComprehensiveVulnerabilitySection
                const vulnSection = await bugBountyCore.formatComprehensiveVulnerabilitySection(testVuln, 1, testVuln.location);
                
                if (vulnSection && vulnSection.length > 100) {
                    addResult('✅ تم إنشاء قسم الثغرة الشامل بنجاح', 'success');
                    addResult(`   الحجم: ${vulnSection.length} حرف`, 'info');
                    
                    // فحص المحتوى للتأكد من عدم وجود نصوص عامة
                    const hasGenericContent = vulnSection.includes('تأثير متغير حسب السياق') ||
                                            vulnSection.includes('مستخرجة تلقائياً من البرومبت') ||
                                            vulnSection.includes('payload_example');
                    
                    if (hasGenericContent) {
                        addResult('⚠️ تم العثور على محتوى عام في التقرير', 'warning');
                    } else {
                        addResult('✅ لا يوجد محتوى عام - التفاصيل مخصصة', 'success');
                    }
                    
                    return true;
                } else {
                    addResult('❌ فشل في إنشاء قسم الثغرة', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار التقارير: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار قسم الثغرة الشامل
        async function testVulnerabilitySection() {
            addResult('🚨 بدء اختبار قسم الثغرة الشامل...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return;
            }

            try {
                const testVuln = createTestVulnerability();
                const vulnerabilities = [testVuln];
                
                // اختبار generateComprehensiveVulnerabilitiesContentUsingExistingFunctions
                const vulnContent = await bugBountyCore.generateComprehensiveVulnerabilitiesContentUsingExistingFunctions(vulnerabilities);
                
                if (vulnContent && vulnContent.length > 100) {
                    addResult('✅ تم إنشاء محتوى الثغرات الشامل بنجاح', 'success');
                    addResult(`   الحجم: ${vulnContent.length} حرف`, 'info');
                    
                    // فحص استخدام التفاصيل الشاملة
                    const usesComprehensiveDetails = testVuln.comprehensive_details !== undefined;
                    
                    if (usesComprehensiveDetails) {
                        addResult('✅ الثغرة تحتوي على comprehensive_details', 'success');
                    } else {
                        addResult('⚠️ الثغرة لا تحتوي على comprehensive_details', 'warning');
                    }
                    
                    return true;
                } else {
                    addResult('❌ فشل في إنشاء محتوى الثغرات', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار قسم الثغرة: ${error.message}`, 'error');
                return false;
            }
        }

        // تشغيل الاختبار الكامل
        async function runFullTest() {
            addResult('🚀 بدء الاختبار الكامل للتفاصيل الشاملة...', 'info');
            document.getElementById('testResults').innerHTML = '';
            
            updateProgress(0);
            
            const tests = [
                { name: 'إنشاء التفاصيل الشاملة', func: testComprehensiveDetailsGeneration },
                { name: 'استخدام التقارير', func: testReportUsage },
                { name: 'قسم الثغرة الشامل', func: testVulnerabilitySection }
            ];
            
            let passedTests = 0;
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                addResult(`\n🧪 اختبار ${i + 1}/${tests.length}: ${test.name}`, 'info');
                
                const result = await test.func();
                if (result) {
                    passedTests++;
                }
                
                updateProgress(((i + 1) / tests.length) * 100);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            addResult(`\n📊 النتائج النهائية: ${passedTests}/${tests.length} اختبار نجح`, 
                     passedTests === tests.length ? 'success' : 'warning');
            
            if (passedTests === tests.length) {
                addResult('🎉 جميع الاختبارات نجحت! التفاصيل الشاملة تعمل بشكل صحيح', 'success');
            } else {
                addResult('⚠️ بعض الاختبارات فشلت - يحتاج إصلاح', 'warning');
            }
        }

        // تهيئة تلقائية عند تحميل الصفحة
        window.addEventListener('load', async () => {
            addResult('🔄 تهيئة نظام الاختبار...', 'info');
            await initializeSystem();
            addResult('✅ نظام الاختبار جاهز!', 'success');
        });
    </script>
</body>
</html>
