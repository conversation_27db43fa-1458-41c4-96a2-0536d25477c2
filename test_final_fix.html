<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Final Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Test Final Fix</h1>
    <button onclick="testFinalFix()">Test Final Fix</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function testFinalFix() {
            document.getElementById('results').innerHTML = '';
            log('🔍 اختبار الإصلاح النهائي...', 'info');
            
            try {
                // Clear cache and load fresh
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const timestamp = new Date().getTime();
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${timestamp}&fix=final`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore بنجاح', 'success');
                
                // Test template loading
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // Test generateFinalComprehensiveReport function
                const testData = {
                    total_vulnerabilities: 3,
                    vulnerabilities: [
                        { name: 'XSS', severity: 'High', description: 'Cross-site scripting vulnerability' },
                        { name: 'SQL Injection', severity: 'Critical', description: 'SQL injection vulnerability' },
                        { name: 'CSRF', severity: 'Medium', description: 'Cross-site request forgery' }
                    ]
                };
                
                log('🧪 اختبار دالة generateFinalComprehensiveReport...', 'info');
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://example.com');
                    
                    if (report && typeof report === 'string' && report.length > 1000) {
                        log(`✅ تم إنشاء التقرير بنجاح - الحجم: ${report.length} حرف`, 'success');
                        
                        // Check if variables are replaced
                        const hasVariables = report.includes('{{');
                        if (hasVariables) {
                            const variables = report.match(/{{[^}]+}}/g);
                            log(`❌ ما زالت هناك متغيرات غير مستبدلة: ${variables ? variables.join(', ') : 'غير معروف'}`, 'error');
                        } else {
                            log('🎉 تم استبدال جميع المتغيرات بنجاح!', 'success');
                        }
                        
                        // Check specific content
                        const hasVulnCount = report.includes('3') && !report.includes('{{TOTAL_VULNERABILITIES}}');
                        const hasTargetUrl = report.includes('https://example.com') && !report.includes('{{TARGET_URL}}');
                        const hasTimestamp = !report.includes('{{TIMESTAMP}}');
                        
                        log(`📊 فحص المحتوى:`, 'info');
                        log(`   - عدد الثغرات: ${hasVulnCount ? '✅' : '❌'}`, hasVulnCount ? 'success' : 'error');
                        log(`   - رابط الموقع: ${hasTargetUrl ? '✅' : '❌'}`, hasTargetUrl ? 'success' : 'error');
                        log(`   - الطابع الزمني: ${hasTimestamp ? '✅' : '❌'}`, hasTimestamp ? 'success' : 'error');
                        
                        if (hasVulnCount && hasTargetUrl && hasTimestamp && !hasVariables) {
                            log('🎉 الإصلاح النهائي نجح بالكامل!', 'success');
                            log('✅ النظام جاهز للاستخدام الفعلي', 'success');
                        } else {
                            log('⚠️ هناك بعض المشاكل المتبقية', 'warning');
                        }
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير أو التقرير فارغ', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ في إنشاء التقرير: ${reportError.message}`, 'error');
                }
                
                // Test page report function
                log('🧪 اختبار دالة generatePageHTMLReport...', 'info');
                
                try {
                    const pageData = {
                        vulnerabilities: [
                            { name: 'XSS', severity: 'High', description: 'Page XSS vulnerability' }
                        ]
                    };
                    
                    const pageReport = await core.generatePageHTMLReport(pageData, 'https://example.com/page1', 1);
                    
                    if (pageReport && typeof pageReport === 'string' && pageReport.length > 1000) {
                        log(`✅ تم إنشاء تقرير الصفحة بنجاح - الحجم: ${pageReport.length} حرف`, 'success');
                        
                        const hasPageVariables = pageReport.includes('{{');
                        if (!hasPageVariables) {
                            log('✅ تم استبدال جميع متغيرات تقرير الصفحة بنجاح!', 'success');
                        } else {
                            const pageVariables = pageReport.match(/{{[^}]+}}/g);
                            log(`❌ متغيرات غير مستبدلة في تقرير الصفحة: ${pageVariables ? pageVariables.join(', ') : 'غير معروف'}`, 'error');
                        }
                    } else {
                        log('❌ فشل في إنشاء تقرير الصفحة', 'error');
                    }
                    
                } catch (pageError) {
                    log(`❌ خطأ في إنشاء تقرير الصفحة: ${pageError.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
    </script>
</body>
</html>
