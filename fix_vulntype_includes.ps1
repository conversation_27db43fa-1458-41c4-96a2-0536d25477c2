# سكريبت لإصلاح جميع استخدامات vulnType.includes
$filePath = "assets\modules\bugbounty\BugBountyCore.js"

Write-Host "🔧 بدء إصلاح جميع استخدامات vulnType.includes..."

# قراءة الملف
$content = Get-Content $filePath -Raw

# عد الاستخدامات قبل الإصلاح
$beforeCount = ([regex]::Matches($content, "vulnType\.includes\(")).Count
Write-Host "📊 عدد الاستخدامات قبل الإصلاح: $beforeCount"

# استبدال جميع الاستخدامات
$content = $content -replace "vulnType\.includes\(", "this.safeIncludes(vulnType, "

# عد الاستخدامات بعد الإصلاح
$afterCount = ([regex]::Matches($content, "vulnType\.includes\(")).Count
Write-Host "📊 عدد الاستخدامات بعد الإصلاح: $afterCount"

# حفظ الملف
Set-Content $filePath $content -Encoding UTF8

Write-Host "✅ تم إصلاح جميع الاستخدامات بنجاح!"
Write-Host "🎯 تم استبدال $($beforeCount - $afterCount) استخدام"
