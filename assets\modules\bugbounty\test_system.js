/**
 * Bug Bounty System v4.0 - اختبار النظام الشامل
 * ملف اختبار شامل للتأكد من عمل جميع المكونات في النظام v4.0
 * يتضمن اختبار الصور الحقيقية والاستغلال الفعلي والبرومبت الكامل
 */

// حماية من التحميل المكرر
if (typeof window.BugBountySystemTester !== 'undefined') {
    console.warn('⚠️ BugBountySystemTester تم تحميله مسبقاً - تجاهل التحميل المكرر');
} else {

class BugBountySystemTester {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Bug Bounty System Tester v4.0';
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
        this.realTestingEnabled = true;
        this.visualTestingEnabled = true;
        this.promptBasedTesting = true;

        console.log(`🧪 ${this.systemName} تم تحميله بنجاح`);
        console.log('✅ ميزات v4.0: اختبار الصور الحقيقية + الاستغلال الفعلي + البرومبت الكامل');
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        console.log('🧪 بدء اختبار نظام Bug Bounty v3.0...');
        
        // اختبار تحميل الملفات الأساسية
        await this.testCoreFilesLoaded();
        
        // اختبار BugBountyCore
        await this.testBugBountyCore();
        
        // اختبار ImpactVisualizer
        await this.testImpactVisualizer();
        
        // اختبار prompt_template.txt
        await this.testPromptTemplate();
        
        // اختبار الدوال المُصدرة
        await this.testExportedFunctions();
        
        // اختبار Python analyzer (إذا كان متاحاً)
        await this.testPythonAnalyzer();
        
        // عرض النتائج النهائية
        this.displayTestResults();
    }

    // اختبار تحميل الملفات الأساسية
    async testCoreFilesLoaded() {
        console.log('📁 اختبار تحميل الملفات الأساسية...');
        
        this.addTest('BugBountyCore Class', typeof window.BugBountyCore === 'function');
        this.addTest('ImpactVisualizer Class', typeof window.ImpactVisualizer === 'function');
        this.addTest('startComprehensiveBugBountyScan Function', typeof window.startComprehensiveBugBountyScan === 'function');
        this.addTest('performRealBugBountyScan Function', typeof window.performRealBugBountyScan === 'function');
        this.addTest('saveReportAsHTML Function', typeof window.saveReportAsHTML === 'function');
    }

    // اختبار BugBountyCore
    async testBugBountyCore() {
        console.log('🛡️ اختبار BugBountyCore...');
        
        try {
            const core = new window.BugBountyCore();
            this.addTest('BugBountyCore Instance Creation', core instanceof window.BugBountyCore);
            
            // اختبار الدوال الأساسية
            this.addTest('collectComprehensiveWebsiteData Method', typeof core.collectComprehensiveWebsiteData === 'function');
            this.addTest('performAIVulnerabilityAnalysis Method', typeof core.performAIVulnerabilityAnalysis === 'function');
            this.addTest('processAIVulnerabilityResults Method', typeof core.processAIVulnerabilityResults === 'function');
            this.addTest('loadPromptTemplate Method', typeof core.loadPromptTemplate === 'function');
            
            // اختبار تحميل البرومبت
            try {
                const prompt = await core.loadPromptTemplate();
                this.addTest('Prompt Template Loading', prompt && prompt.length > 100);
                this.addTest('Prompt Contains Bug Bounty Instructions', prompt.includes('Bug Bounty') || prompt.includes('ثغرات'));
            } catch (error) {
                this.addTest('Prompt Template Loading', false, error.message);
            }
            
        } catch (error) {
            this.addTest('BugBountyCore Instance Creation', false, error.message);
        }
    }

    // اختبار ImpactVisualizer
    async testImpactVisualizer() {
        console.log('📸 اختبار ImpactVisualizer...');
        
        try {
            const visualizer = new window.ImpactVisualizer();
            this.addTest('ImpactVisualizer Instance Creation', visualizer instanceof window.ImpactVisualizer);
            
            // اختبار الدوال الأساسية
            this.addTest('createVulnerabilityVisualization Method', typeof visualizer.createVulnerabilityVisualization === 'function');
            this.addTest('simulateSecureExploitation Method', typeof visualizer.simulateSecureExploitation === 'function');
            this.addTest('getAllVisualizations Method', typeof visualizer.getAllVisualizations === 'function');
            this.addTest('exportVisualizations Method', typeof visualizer.exportVisualizations === 'function');
            
            // اختبار إنشاء تصور تجريبي
            const testVulnerability = {
                name: 'Test XSS',
                category: 'Injection',
                severity: 'High',
                cvss: 7.4,
                impact: 'Test impact',
                remediation: 'Test remediation'
            };
            
            const testWebsiteData = {
                url: 'https://example.com',
                domain: 'example.com'
            };
            
            try {
                const visualization = await visualizer.createVulnerabilityVisualization(testVulnerability, testWebsiteData);
                this.addTest('Vulnerability Visualization Creation', visualization && visualization.vulnerability_name === 'Test XSS');
            } catch (error) {
                this.addTest('Vulnerability Visualization Creation', false, error.message);
            }
            
        } catch (error) {
            this.addTest('ImpactVisualizer Instance Creation', false, error.message);
        }
    }

    // اختبار prompt_template.txt
    async testPromptTemplate() {
        console.log('📄 اختبار prompt_template.txt...');
        
        try {
            const response = await fetch('assets/modules/bugbounty/prompt_template.txt');
            this.addTest('Prompt Template File Accessible', response.ok);
            
            if (response.ok) {
                const content = await response.text();
                this.addTest('Prompt Template Content Not Empty', content.length > 0);
                this.addTest('Prompt Contains Arabic Instructions', content.includes('المهمة') || content.includes('الفحص'));
                this.addTest('Prompt Contains Vulnerability Types', content.includes('SQL Injection') || content.includes('XSS'));
                this.addTest('Prompt Contains CVSS References', content.includes('CVSS') || content.includes('نقاط'));
                this.addTest('Prompt Contains Report Format', content.includes('تقرير') || content.includes('التقرير'));
            }
        } catch (error) {
            this.addTest('Prompt Template File Accessible', false, error.message);
        }
    }

    // اختبار الدوال المُصدرة
    async testExportedFunctions() {
        console.log('🔗 اختبار الدوال المُصدرة...');
        
        const requiredFunctions = [
            'startComprehensiveBugBountyScan',
            'performRealBugBountyScan',
            'runPythonAnalyzer',
            'sendToAIModel',
            'formatSecurityReport',
            'formatEnhancedSecurityReport',
            'saveReportAsHTML',
            'displayFinalSecurityReport'
        ];
        
        requiredFunctions.forEach(funcName => {
            this.addTest(`${funcName} Function Exported`, typeof window[funcName] === 'function');
        });
    }

    // اختبار Python analyzer
    async testPythonAnalyzer() {
        console.log('🐍 اختبار Python analyzer...');
        
        // فحص وجود ملف analyzer.py
        try {
            const response = await fetch('assets/modules/bugbounty/analyzer.py');
            this.addTest('Python Analyzer File Accessible', response.ok);
            
            if (response.ok) {
                const content = await response.text();
                this.addTest('Python Analyzer Content Not Empty', content.length > 0);
                this.addTest('Python Contains WebsiteAnalyzer Class', content.includes('class WebsiteAnalyzer'));
                this.addTest('Python Contains Security Headers Check', content.includes('check_security_headers'));
                this.addTest('Python Contains Vulnerability Detection', content.includes('detect_potential_vulnerabilities'));
            }
        } catch (error) {
            this.addTest('Python Analyzer File Accessible', false, error.message);
        }
        
        // اختبار تشغيل Python (إذا كان متاحاً)
        if (typeof window.runPythonAnalyzer === 'function') {
            try {
                // محاولة تشغيل تحليل تجريبي
                const testResult = await window.runPythonAnalyzer('https://example.com');
                this.addTest('Python Analyzer Execution', testResult !== null);
            } catch (error) {
                this.addTest('Python Analyzer Execution', false, 'Python not available or execution failed');
            }
        }
    }

    // إضافة نتيجة اختبار
    addTest(testName, passed, errorMessage = null) {
        const result = {
            name: testName,
            passed: passed,
            error: errorMessage
        };
        
        this.testResults.push(result);
        
        if (passed) {
            this.passedTests++;
            console.log(`✅ ${testName}: PASSED`);
        } else {
            this.failedTests++;
            console.log(`❌ ${testName}: FAILED${errorMessage ? ` - ${errorMessage}` : ''}`);
        }
    }

    // عرض النتائج النهائية
    displayTestResults() {
        console.log('\n' + '='.repeat(60));
        console.log('🧪 نتائج اختبار نظام Bug Bounty v3.0');
        console.log('='.repeat(60));
        
        console.log(`📊 إجمالي الاختبارات: ${this.testResults.length}`);
        console.log(`✅ نجح: ${this.passedTests}`);
        console.log(`❌ فشل: ${this.failedTests}`);
        console.log(`📈 معدل النجاح: ${Math.round((this.passedTests / this.testResults.length) * 100)}%`);
        
        if (this.failedTests > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            this.testResults.filter(test => !test.passed).forEach(test => {
                console.log(`  - ${test.name}${test.error ? `: ${test.error}` : ''}`);
            });
        }
        
        console.log('\n' + '='.repeat(60));
        
        if (this.failedTests === 0) {
            console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
        } else if (this.passedTests > this.failedTests) {
            console.log('⚠️ معظم الاختبارات نجحت، النظام يعمل مع بعض القيود.');
        } else {
            console.log('🚨 عدد كبير من الاختبارات فشل، يحتاج النظام لمراجعة.');
        }
        
        return {
            total: this.testResults.length,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: Math.round((this.passedTests / this.testResults.length) * 100)
        };
    }

    // اختبار سريع للنظام
    async quickTest() {
        console.log('⚡ اختبار سريع للنظام...');
        
        const quickTests = [
            { name: 'BugBountyCore', check: () => typeof window.BugBountyCore === 'function' },
            { name: 'ImpactVisualizer', check: () => typeof window.ImpactVisualizer === 'function' },
            { name: 'Main Scan Function', check: () => typeof window.startComprehensiveBugBountyScan === 'function' },
            { name: 'Save Report Function', check: () => typeof window.saveReportAsHTML === 'function' }
        ];
        
        let passed = 0;
        quickTests.forEach(test => {
            const result = test.check();
            if (result) {
                passed++;
                console.log(`✅ ${test.name}: OK`);
            } else {
                console.log(`❌ ${test.name}: FAILED`);
            }
        });
        
        console.log(`\n📊 النتيجة السريعة: ${passed}/${quickTests.length} (${Math.round((passed/quickTests.length)*100)}%)`);
        
        return passed === quickTests.length;
    }
}

// تصدير الكلاس للاستخدام
window.BugBountySystemTester = BugBountySystemTester;

// تشغيل اختبار سريع عند التحميل
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🔧 تشغيل اختبار سريع لنظام Bug Bounty...');
    
    const tester = new BugBountySystemTester();
    const quickResult = await tester.quickTest();
    
    if (quickResult) {
        console.log('🎉 الاختبار السريع نجح! النظام جاهز.');
    } else {
        console.log('⚠️ الاختبار السريع فشل، قد تحتاج لمراجعة النظام.');
    }
});

// دالة مساعدة لتشغيل الاختبار الكامل من console
window.runBugBountyTests = async () => {
    const tester = new BugBountySystemTester();
    return await tester.runAllTests();
};

console.log('🧪 Bug Bounty System Tester v3.0 محمل ومُفعل');
console.log('💡 لتشغيل الاختبار الكامل: runBugBountyTests()');

} // إغلاق حماية التحميل المكرر
