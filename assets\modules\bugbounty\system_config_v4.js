/**
 * Bug Bounty System v4.0 - تكوين النظام الشامل
 * ملف التكوين الرئيسي للنظام v4.0
 */

class BugBountySystemConfig {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Bug Bounty System v4.0 - Complete Edition';
        this.buildDate = new Date().toISOString();
        
        // إعدادات النظام الأساسية
        this.config = {
            // معلومات النظام
            system: {
                version: '4.0',
                name: 'Bug Bounty System v4.0',
                description: 'نظام فحص الثغرات الأمنية الشامل مع الاستغلال الحقيقي والصور الفعلية',
                author: 'Bug Bounty Team',
                license: 'Professional Security Testing',
                build_date: this.buildDate
            },
            
            // ميزات النظام v4.0
            features: {
                prompt_based_analysis: true,
                real_exploitation: true,
                visual_screenshots: true,
                comprehensive_testing: true,
                ai_integration: true,
                python_analyzer: true,
                impact_visualizer: true,
                professional_reporting: true,
                multi_format_export: true,
                real_time_analysis: true
            },
            
            // مكونات النظام
            components: {
                core: 'BugBountyCore.js',
                visualizer: 'impact_visualizer.js',
                prompt_system: 'prompt_only_system.js',
                python_analyzer: 'analyzer.py',
                report_exporter: 'report_exporter.js',
                test_system: 'test_system.js',
                prompt_template: 'prompt_template.txt',
                report_template: 'report_template.html',
                styles: 'style.css'
            },
            
            // إعدادات الفحص
            scanning: {
                max_timeout: 30000,
                max_retries: 3,
                concurrent_requests: 5,
                screenshot_quality: 'high',
                exploitation_safety: 'safe_mode',
                comprehensive_depth: 'deep'
            },
            
            // إعدادات الذكاء الاصطناعي
            ai: {
                enabled: true,
                models: ['openrouter', 'local_model'],
                temperature: 0.1,
                max_tokens: 6000,
                system_prompt_source: 'prompt_template.txt'
            },
            
            // إعدادات التصوير
            screenshots: {
                enabled: true,
                format: 'png',
                quality: 0.9,
                width: 1200,
                height: 800,
                before_after: true,
                exploitation_proof: true
            },
            
            // إعدادات الاستغلال
            exploitation: {
                enabled: true,
                safe_mode: true,
                real_testing: true,
                payload_testing: true,
                impact_demonstration: true,
                visual_proof: true
            },
            
            // إعدادات التقارير
            reporting: {
                formats: ['html', 'pdf', 'json', 'txt'],
                include_screenshots: true,
                include_exploitation_proof: true,
                include_remediation: true,
                professional_layout: true,
                arabic_support: true
            }
        };
        
        console.log(`⚙️ ${this.systemName} - تكوين النظام تم تحميله بنجاح`);
        this.displaySystemInfo();
    }
    
    // عرض معلومات النظام
    displaySystemInfo() {
        console.log('\n🚀 معلومات النظام:');
        console.log(`📋 الاسم: ${this.config.system.name}`);
        console.log(`🔢 الإصدار: ${this.config.system.version}`);
        console.log(`📅 تاريخ البناء: ${this.config.system.build_date}`);
        console.log(`📝 الوصف: ${this.config.system.description}`);
        
        console.log('\n✨ الميزات المُفعلة:');
        Object.entries(this.config.features).forEach(([feature, enabled]) => {
            if (enabled) {
                console.log(`  ✅ ${feature.replace(/_/g, ' ')}`);
            }
        });
        
        console.log('\n📦 المكونات:');
        Object.entries(this.config.components).forEach(([component, file]) => {
            console.log(`  📄 ${component}: ${file}`);
        });
    }
    
    // التحقق من توافق النظام
    async checkSystemCompatibility() {
        console.log('🔍 فحص توافق النظام...');
        
        const compatibility = {
            core_loaded: typeof window.BugBountyCore === 'function',
            visualizer_loaded: typeof window.ImpactVisualizer === 'function',
            prompt_system_loaded: typeof window.PromptOnlyBugBountySystem === 'function',
            report_exporter_loaded: typeof window.BugBountyReportExporter === 'function',
            test_system_loaded: typeof window.BugBountySystemTester === 'function',
            main_functions_loaded: typeof window.startComprehensiveBugBountyScan === 'function',
            canvas_support: !!document.createElement('canvas').getContext,
            fetch_support: typeof fetch === 'function',
            promise_support: typeof Promise === 'function'
        };
        
        const compatibilityScore = Object.values(compatibility).filter(Boolean).length;
        const totalChecks = Object.keys(compatibility).length;
        const percentage = Math.round((compatibilityScore / totalChecks) * 100);
        
        console.log(`📊 نتيجة التوافق: ${compatibilityScore}/${totalChecks} (${percentage}%)`);
        
        if (percentage >= 90) {
            console.log('✅ النظام متوافق بالكامل');
        } else if (percentage >= 70) {
            console.log('⚠️ النظام متوافق مع بعض القيود');
        } else {
            console.log('❌ النظام غير متوافق - يحتاج مراجعة');
        }
        
        return {
            compatible: percentage >= 70,
            score: percentage,
            details: compatibility
        };
    }
    
    // تحديث إعدادات النظام
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ تم تحديث إعدادات النظام');
        return this.config;
    }
    
    // الحصول على إعدادات محددة
    getConfig(section = null) {
        if (section) {
            return this.config[section] || null;
        }
        return this.config;
    }
    
    // تصدير التكوين
    exportConfig() {
        return JSON.stringify(this.config, null, 2);
    }
    
    // تحميل تكوين من JSON
    loadConfig(configJson) {
        try {
            const newConfig = JSON.parse(configJson);
            this.updateConfig(newConfig);
            return true;
        } catch (error) {
            console.error('❌ خطأ في تحميل التكوين:', error);
            return false;
        }
    }
}

// إنشاء مثيل التكوين العام
window.bugBountyConfig = new BugBountySystemConfig();

// تصدير الكلاس
window.BugBountySystemConfig = BugBountySystemConfig;

// دوال مساعدة سريعة
window.getSystemConfig = () => window.bugBountyConfig.getConfig();
window.checkSystemCompatibility = () => window.bugBountyConfig.checkSystemCompatibility();
window.getSystemInfo = () => window.bugBountyConfig.config.system;

console.log('⚙️ Bug Bounty System v4.0 Configuration تم تحميله بنجاح');
console.log('💡 للحصول على التكوين: getSystemConfig()');
console.log('💡 لفحص التوافق: checkSystemCompatibility()');
console.log('💡 لمعلومات النظام: getSystemInfo()');
