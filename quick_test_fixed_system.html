<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للنظام المُصحح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
            color: white;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #fff;
            border-left: 4px solid #4ecdc4;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .loading {
            color: #f39c12;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار سريع للنظام المُصحح v4.0</h1>
            <p>اختبار النظام الجديد مع الاختبار الحقيقي والصور الحقيقية</p>
        </div>

        <button class="btn" onclick="testFixedSystem()">🚀 اختبار النظام المُصحح الآن</button>
        <div id="testResult" class="result"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            try {
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تحميل BugBountyCore بنجاح');
                
                // إضافة رسالة جاهزية
                const readyMsg = document.createElement('div');
                readyMsg.className = 'result';
                readyMsg.innerHTML = '<div class="success">✅ النظام جاهز للاختبار</div>';
                document.body.appendChild(readyMsg);
                
            } catch (error) {
                console.error('❌ خطأ في تحميل BugBountyCore:', error);
                
                const errorMsg = document.createElement('div');
                errorMsg.className = 'result';
                errorMsg.innerHTML = `<div class="error">❌ خطأ في تحميل النظام: ${error.message}</div>`;
                document.body.appendChild(errorMsg);
            }
        });

        // اختبار النظام المُصحح
        async function testFixedSystem() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div class="loading">🔄 جاري اختبار النظام المُصحح...</div>';

            try {
                console.log('🔥 بدء اختبار النظام المُصحح v4.0...');
                
                if (!bugBountyCore) {
                    throw new Error('BugBountyCore غير محمل');
                }

                // اختبار الدالة الجديدة
                console.log('📞 استدعاء performRealVulnerabilityTestingAndAnalysis...');
                const testUrl = 'https://example.com';
                const startTime = Date.now();
                
                const realAnalysis = await bugBountyCore.performRealVulnerabilityTestingAndAnalysis(testUrl);
                
                const endTime = Date.now();
                const duration = endTime - startTime;

                console.log('✅ تم الحصول على النتيجة:', realAnalysis);

                // فحص النتيجة
                let analysisCheck = '';
                if (realAnalysis) {
                    if (typeof realAnalysis === 'string') {
                        analysisCheck = `
                            <div class="success">✅ تم إرجاع نص التحليل</div>
                            <div>📏 طول النص: ${realAnalysis.length} حرف</div>
                        `;
                        
                        // فحص وجود الصور
                        if (realAnalysis.includes('<img src="data:image/png;base64,')) {
                            analysisCheck += '<div class="success">🖼️ تم العثور على صور مضمنة في التحليل!</div>';
                        } else {
                            analysisCheck += '<div class="error">❌ لم يتم العثور على صور مضمنة</div>';
                        }
                        
                        // فحص استبدال النصوص الوهمية
                        if (realAnalysis.includes('payload مخصص') || realAnalysis.includes('معامل مكتشف')) {
                            analysisCheck += '<div class="error">❌ لا تزال توجد نصوص وهمية</div>';
                        } else {
                            analysisCheck += '<div class="success">✅ تم استبدال النصوص الوهمية</div>';
                        }
                        
                    } else {
                        analysisCheck = `<div class="error">❌ نوع النتيجة غير متوقع: ${typeof realAnalysis}</div>`;
                    }
                } else {
                    analysisCheck = '<div class="error">❌ لم يتم إرجاع أي نتيجة</div>';
                }

                resultDiv.innerHTML = `
                    <h4>🎉 نتيجة اختبار النظام المُصحح:</h4>
                    <div class="success">✅ تم تشغيل النظام بنجاح!</div>
                    <div>⏱️ مدة التنفيذ: ${duration}ms</div>
                    ${analysisCheck}
                    
                    <h5>📋 تفاصيل النتيجة:</h5>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; font-size: 12px;">
                        ${realAnalysis ? realAnalysis.substring(0, 1000).replace(/\n/g, '<br>') + (realAnalysis.length > 1000 ? '...' : '') : 'لا توجد بيانات'}
                    </div>
                    
                    <div class="success" style="margin-top: 15px; padding: 10px; background: #d4edda; border-radius: 5px;">
                        <strong>🔥 تقييم النظام:</strong><br>
                        ${realAnalysis ? '✅ النظام يعمل ويُنتج محتوى' : '❌ النظام لا يُنتج محتوى'}<br>
                        ${realAnalysis && realAnalysis.includes('<img') ? '✅ الصور مضمنة' : '❌ الصور غير مضمنة'}<br>
                        ${realAnalysis && !realAnalysis.includes('payload مخصص') ? '✅ لا توجد نصوص وهمية' : '❌ توجد نصوص وهمية'}
                    </div>
                `;

            } catch (error) {
                console.error('❌ خطأ في اختبار النظام:', error);
                
                resultDiv.innerHTML = `
                    <div class="error">❌ خطأ في اختبار النظام: ${error.message}</div>
                    <div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 12px;">
                        <strong>تفاصيل الخطأ:</strong><br>
                        ${error.stack ? error.stack.replace(/\n/g, '<br>') : error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
