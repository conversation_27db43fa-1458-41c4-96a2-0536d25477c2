<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Detailed Diagnostic Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .diagnostic-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 600px;
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            border: 2px solid #00ff00;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            z-index: 10000;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }
        .diagnostic-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            color: #ffff00;
            font-weight: bold;
        }
        .diagnostic-logs {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 5px;
            background: #000;
        }
        .diagnostic-entry {
            margin: 2px 0;
            padding: 2px;
            border-left: 3px solid #00ff00;
            padding-left: 5px;
        }
        .diagnostic-error { border-left-color: #ff0000; color: #ff6666; }
        .diagnostic-warn { border-left-color: #ffaa00; color: #ffcc66; }
        .diagnostic-info { border-left-color: #00aaff; color: #66ccff; }
        .diagnostic-success { border-left-color: #00ff00; color: #66ff66; }
    </style>
</head>
<body>
    <h1>Detailed Diagnostic Test</h1>
    <button onclick="detailedTest()">Run Detailed Test</button>
    <div id="results"></div>

    <!-- نافذة التشخيص المنفصلة -->
    <div class="diagnostic-panel" id="diagnosticPanel">
        <div class="diagnostic-header">
            <span>🔍 Bug Bounty Diagnostic</span>
            <button onclick="document.getElementById('diagnosticPanel').style.display='none'" 
                    style="background: #ff0000; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">✕</button>
        </div>
        <div class="diagnostic-logs" id="diagnosticLogs"></div>
        <div style="margin-top: 10px; text-align: center;">
            <button onclick="exportDiagnosticLogs()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin: 2px;">Export</button>
            <button onclick="clearDiagnosticLogs()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin: 2px;">Clear</button>
        </div>
    </div>

    <script>
        // نظام التشخيص المنفصل
        class DetailedDiagnostic {
            constructor() {
                this.logs = [];
                this.startTime = new Date();
                this.logContainer = document.getElementById('diagnosticLogs');
            }

            log(level, category, message, data = null) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = {
                    timestamp,
                    level,
                    category,
                    message,
                    data,
                    elapsed: Date.now() - this.startTime.getTime()
                };

                this.logs.push(logEntry);
                this.displayLog(logEntry);
                
                // تسجيل في وحدة التحكم
                const consoleMessage = `[${timestamp}] [${level}] [${category}] ${message}`;
                switch (level) {
                    case 'ERROR':
                        console.error(consoleMessage, data);
                        break;
                    case 'WARN':
                        console.warn(consoleMessage, data);
                        break;
                    case 'INFO':
                        console.info(consoleMessage, data);
                        break;
                    default:
                        console.log(consoleMessage, data);
                }
            }

            displayLog(logEntry) {
                if (!this.logContainer) return;

                const logDiv = document.createElement('div');
                logDiv.className = `diagnostic-entry diagnostic-${logEntry.level.toLowerCase()}`;

                const dataStr = logEntry.data ? ` | ${JSON.stringify(logEntry.data).substring(0, 50)}...` : '';
                logDiv.innerHTML = `
                    <span style="color: #888;">[${logEntry.timestamp}]</span>
                    <span>[${logEntry.level}]</span>
                    <span style="color: #ffff00;">[${logEntry.category}]</span>
                    <span>${logEntry.message}</span>
                    <span style="color: #888; font-size: 10px;">${dataStr}</span>
                `;

                this.logContainer.appendChild(logDiv);
                this.logContainer.scrollTop = this.logContainer.scrollHeight;

                // الحد من عدد السجلات
                if (this.logContainer.children.length > 200) {
                    this.logContainer.removeChild(this.logContainer.firstChild);
                }
            }

            error(category, message, data) { this.log('ERROR', category, message, data); }
            warn(category, message, data) { this.log('WARN', category, message, data); }
            info(category, message, data) { this.log('INFO', category, message, data); }
            success(category, message, data) { this.log('SUCCESS', category, message, data); }
        }

        const diagnostic = new DetailedDiagnostic();

        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        function exportDiagnosticLogs() {
            const logsText = diagnostic.logs.map(log => 
                `[${log.timestamp}] [${log.level}] [${log.category}] ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
            ).join('\n');

            const blob = new Blob([logsText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bug-bounty-diagnostic-${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearDiagnosticLogs() {
            diagnostic.logs = [];
            diagnostic.logContainer.innerHTML = '';
        }

        async function detailedTest() {
            document.getElementById('results').innerHTML = '';
            diagnostic.logs = [];
            diagnostic.logContainer.innerHTML = '';
            
            log('🔍 بدء الاختبار التشخيصي المفصل...', 'info');
            diagnostic.info('TEST', 'بدء الاختبار التشخيصي المفصل');
            
            try {
                // 1. تحميل النظام
                diagnostic.info('LOADING', 'بدء تحميل النظام');
                
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                    diagnostic.info('CLEANUP', 'تم حذف BugBountyCore القديم');
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}&diagnostic=true`;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        diagnostic.success('LOADING', 'تم تحميل BugBountyCore بنجاح');
                        resolve();
                    };
                    script.onerror = (error) => {
                        diagnostic.error('LOADING', 'فشل في تحميل BugBountyCore', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore', 'success');
                diagnostic.success('INIT', 'BugBountyCore متاح');
                
                // 2. إنشاء مثيل
                diagnostic.info('INIT', 'إنشاء مثيل BugBountyCore');
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                diagnostic.success('INIT', 'تم إنشاء المثيل بنجاح');
                
                // 3. تحميل القالب
                diagnostic.info('TEMPLATE', 'بدء تحميل القالب');
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                
                if (!templateResponse.ok) {
                    throw new Error(`فشل تحميل القالب: ${templateResponse.status}`);
                }
                
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحميل القالب - الحجم: ${templateHTML.length} حرف`, 'success');
                diagnostic.success('TEMPLATE', 'تم تحميل القالب وحفظه', { size: templateHTML.length });
                
                // 4. اختبار الدالة مع تسجيل مفصل
                diagnostic.info('REPORT', 'بدء اختبار إنشاء التقرير');
                
                // تسجيل console.log مؤقتاً
                const originalLog = console.log;
                const originalError = console.error;
                const capturedLogs = [];
                
                console.log = (...args) => {
                    const message = args.join(' ');
                    capturedLogs.push({ type: 'log', message });
                    diagnostic.info('CONSOLE', message);
                    originalLog(...args);
                };
                
                console.error = (...args) => {
                    const message = args.join(' ');
                    capturedLogs.push({ type: 'error', message });
                    diagnostic.error('CONSOLE', message);
                    originalError(...args);
                };
                
                try {
                    const testData = {
                        total_vulnerabilities: 2,
                        vulnerabilities: [
                            { name: 'Test XSS', severity: 'High', description: 'Test XSS vulnerability' },
                            { name: 'Test SQL', severity: 'Critical', description: 'Test SQL injection' }
                        ]
                    };
                    
                    diagnostic.info('REPORT', 'استدعاء generateFinalComprehensiveReport', testData);
                    
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test-site.com');
                    
                    // استعادة console
                    console.log = originalLog;
                    console.error = originalError;
                    
                    if (report && report.length > 1000) {
                        log(`✅ نجح إنشاء التقرير! الحجم: ${report.length} حرف`, 'success');
                        diagnostic.success('REPORT', 'تم إنشاء التقرير بنجاح', { size: report.length });
                        
                        // فحص المتغيرات
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length}`, 'warning');
                            diagnostic.warn('REPORT', 'متغيرات غير مستبدلة', { count: variables.length, variables: variables.slice(0, 5) });
                        } else {
                            log('🎉 تم استبدال جميع المتغيرات!', 'success');
                            diagnostic.success('REPORT', 'تم استبدال جميع المتغيرات');
                        }
                        
                        // إنشاء رابط تحميل
                        const blob = new Blob([report], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'detailed-diagnostic-report.html';
                        link.textContent = 'تحميل التقرير المفصل';
                        link.style.display = 'block';
                        link.style.margin = '10px 0';
                        link.style.padding = '10px';
                        link.style.background = '#28a745';
                        link.style.color = 'white';
                        link.style.textDecoration = 'none';
                        link.style.borderRadius = '5px';
                        
                        document.getElementById('results').appendChild(link);
                        
                        diagnostic.success('EXPORT', 'تم إنشاء رابط التحميل');
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                        diagnostic.error('REPORT', 'فشل في إنشاء التقرير', { 
                            reportExists: !!report,
                            reportType: typeof report,
                            reportLength: report ? report.length : 0
                        });
                    }
                    
                } catch (reportError) {
                    console.log = originalLog;
                    console.error = originalError;
                    
                    log(`❌ خطأ في إنشاء التقرير: ${reportError.message}`, 'error');
                    diagnostic.error('REPORT', 'خطأ في إنشاء التقرير', {
                        message: reportError.message,
                        stack: reportError.stack,
                        name: reportError.name
                    });
                    
                    // عرض آخر سجلات مهمة
                    const importantLogs = capturedLogs.filter(entry => 
                        entry.message.includes('خطأ') || 
                        entry.message.includes('فشل') ||
                        entry.message.includes('ERROR') ||
                        entry.message.includes('templateHTML') ||
                        entry.message.includes('القالب')
                    );
                    
                    log('📋 آخر سجلات مهمة:', 'info');
                    importantLogs.slice(-10).forEach(entry => {
                        log(`   [${entry.type.toUpperCase()}] ${entry.message}`, entry.type === 'error' ? 'error' : 'info');
                    });
                }
                
                // إحصائيات التشخيص
                const stats = {
                    total: diagnostic.logs.length,
                    errors: diagnostic.logs.filter(log => log.level === 'ERROR').length,
                    warnings: diagnostic.logs.filter(log => log.level === 'WARN').length,
                    success: diagnostic.logs.filter(log => log.level === 'SUCCESS').length
                };
                
                log(`📊 إحصائيات التشخيص: ${stats.total} سجل (${stats.errors} أخطاء، ${stats.warnings} تحذيرات، ${stats.success} نجاح)`, 'info');
                diagnostic.info('STATS', 'إحصائيات التشخيص النهائية', stats);
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                diagnostic.error('GENERAL', 'خطأ عام في الاختبار', {
                    message: error.message,
                    stack: error.stack
                });
            }
        }
    </script>
</body>
</html>
