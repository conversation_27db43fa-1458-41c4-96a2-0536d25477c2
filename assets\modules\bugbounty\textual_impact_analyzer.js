/**
 * Textual Impact Analyzer v4.0
 * تحليل التأثير النصي والسلوكي للثغرات الأمنية
 * يتكامل مع ImpactVisualizer لتوفير تحليل شامل للتغيرات
 * 
 * @version 4.0.0
 * <AUTHOR> Assistant AI
 * @description محلل التأثير النصي المتقدم للثغرات الأمنية
 */

console.log('🔧 تحميل TextualImpactAnalyzer v4.0...');

class TextualImpactAnalyzer {
    constructor() {
        this.version = '4.0';
        this.systemName = 'Textual Impact Analyzer v4.0';
        this.analysisResults = [];
        this.domComparisonEngine = this.initializeDOMComparisonEngine();
        this.behavioralAnalysisEngine = this.initializeBehavioralAnalysisEngine();
        this.textualChangeDetector = this.initializeTextualChangeDetector();
        
        console.log(`✅ ${this.systemName} تم تحميله بنجاح`);
        console.log('🎯 ميزات v4.0: تحليل نصي متقدم + كشف تغيرات DOM + تحليل سلوكي');
    }

    // تهيئة محرك مقارنة DOM
    initializeDOMComparisonEngine() {
        return {
            compareBeforeAfter: (beforeDOM, afterDOM) => {
                return this.performDOMComparison(beforeDOM, afterDOM);
            },
            detectNewElements: (beforeDOM, afterDOM) => {
                return this.detectNewDOMElements(beforeDOM, afterDOM);
            },
            detectRemovedElements: (beforeDOM, afterDOM) => {
                return this.detectRemovedDOMElements(beforeDOM, afterDOM);
            },
            detectModifiedElements: (beforeDOM, afterDOM) => {
                return this.detectModifiedDOMElements(beforeDOM, afterDOM);
            }
        };
    }

    // تهيئة محرك التحليل السلوكي
    initializeBehavioralAnalysisEngine() {
        return {
            analyzeResponseTime: (beforeTime, afterTime) => {
                return this.analyzeBehavioralResponseTime(beforeTime, afterTime);
            },
            detectErrorMessages: (responseText) => {
                return this.detectErrorMessagesInResponse(responseText);
            },
            analyzeContentChanges: (beforeContent, afterContent) => {
                return this.analyzeBehavioralContentChanges(beforeContent, afterContent);
            },
            detectSecurityBypass: (vulnerability, responseData) => {
                return this.detectSecurityBypassBehavior(vulnerability, responseData);
            }
        };
    }

    // تهيئة كاشف التغيرات النصية
    initializeTextualChangeDetector() {
        return {
            extractErrorMessages: (text) => {
                return this.extractErrorMessagesFromText(text);
            },
            detectDataExposure: (text, vulnerability) => {
                return this.detectDataExposureInText(text, vulnerability);
            },
            analyzeTextualChanges: (beforeText, afterText) => {
                return this.analyzeTextualDifferences(beforeText, afterText);
            },
            detectInjectionEffects: (text, payload) => {
                return this.detectInjectionEffectsInText(text, payload);
            }
        };
    }

    // الدالة الرئيسية لتحليل تفاصيل الثغرة
    async analyzeVulnerabilityDetails(vulnerability, websiteData, exploitationResult) {
        console.log(`🔬 تحليل تفاصيل الثغرة: ${vulnerability.name}`);

        const analysis = {
            vulnerability_name: vulnerability.name,
            vulnerability_type: vulnerability.category,
            timestamp: new Date().toISOString(),
            
            // التحليل النصي المتقدم
            textual_analysis: await this.performTextualAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل تغيرات DOM
            dom_analysis: await this.performDOMAnalysis(vulnerability, websiteData, exploitationResult),
            
            // التحليل السلوكي
            behavioral_analysis: await this.performBehavioralAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل التأثير على النظام
            system_impact_analysis: await this.performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult),
            
            // تحليل الأدوات والمصادر
            tools_and_sources: await this.analyzeToolsAndSources(vulnerability, exploitationResult),
            
            // تحليل التقييم والمثابرة
            persistence_analysis: await this.analyzePersistenceAndReproducibility(vulnerability, exploitationResult)
        };

        this.analysisResults.push(analysis);
        return analysis;
    }

    // تحليل نصي متقدم
    async performTextualAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('📝 تحليل نصي متقدم...');

        const textualAnalysis = {
            description: this.generateDynamicDescription(vulnerability, exploitationResult),
            payloads_used: this.extractPayloadsUsed(vulnerability, exploitationResult),
            error_messages_detected: this.extractErrorMessages(exploitationResult),
            data_exposure_detected: this.analyzeDataExposure(vulnerability, exploitationResult),
            textual_changes: this.analyzeTextualChanges(vulnerability, exploitationResult),
            injection_effects: this.analyzeInjectionEffects(vulnerability, exploitationResult)
        };

        return textualAnalysis;
    }

    // تحليل تغيرات DOM
    async performDOMAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🌐 تحليل تغيرات DOM...');

        const domAnalysis = {
            dom_changes: this.analyzeDOMChanges(vulnerability, exploitationResult),
            new_elements_added: this.detectNewElements(exploitationResult),
            elements_modified: this.detectModifiedElements(exploitationResult),
            elements_removed: this.detectRemovedElements(exploitationResult),
            layout_changes: this.analyzeLayoutChanges(vulnerability, exploitationResult),
            style_modifications: this.analyzeStyleModifications(exploitationResult)
        };

        return domAnalysis;
    }

    // التحليل السلوكي
    async performBehavioralAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('🎭 التحليل السلوكي...');

        const behavioralAnalysis = {
            response_time_changes: this.analyzeResponseTimeChanges(exploitationResult),
            server_behavior_changes: this.analyzeServerBehaviorChanges(vulnerability, exploitationResult),
            session_impact: this.analyzeSessionImpact(vulnerability, exploitationResult),
            authentication_bypass: this.analyzeAuthenticationBypass(vulnerability, exploitationResult),
            authorization_changes: this.analyzeAuthorizationChanges(vulnerability, exploitationResult),
            performance_impact: this.analyzePerformanceImpact(exploitationResult)
        };

        return behavioralAnalysis;
    }

    // تحليل التأثير على النظام
    async performSystemImpactAnalysis(vulnerability, websiteData, exploitationResult) {
        console.log('⚙️ تحليل التأثير على النظام...');

        const systemImpact = {
            backend_impact: this.analyzeBackendImpact(vulnerability, exploitationResult),
            database_impact: this.analyzeDatabaseImpact(vulnerability, exploitationResult),
            file_system_impact: this.analyzeFileSystemImpact(vulnerability, exploitationResult),
            network_impact: this.analyzeNetworkImpact(vulnerability, exploitationResult),
            security_controls_bypassed: this.analyzeSecurityControlsBypass(vulnerability, exploitationResult),
            data_integrity_impact: this.analyzeDataIntegrityImpact(vulnerability, exploitationResult)
        };

        return systemImpact;
    }

    // تحليل الأدوات والمصادر
    async analyzeToolsAndSources(vulnerability, exploitationResult) {
        console.log('🛠️ تحليل الأدوات والمصادر...');

        const toolsAnalysis = {
            exploitation_tools: this.identifyExploitationTools(vulnerability),
            detection_methods: this.identifyDetectionMethods(vulnerability),
            payload_sources: this.identifyPayloadSources(vulnerability, exploitationResult),
            verification_tools: this.identifyVerificationTools(vulnerability),
            mitigation_tools: this.identifyMitigationTools(vulnerability)
        };

        return toolsAnalysis;
    }

    // تحليل التقييم والمثابرة
    async analyzePersistenceAndReproducibility(vulnerability, exploitationResult) {
        console.log('🔄 تحليل التقييم والمثابرة...');

        const persistenceAnalysis = {
            reproducibility_rate: this.calculateReproducibilityRate(vulnerability, exploitationResult),
            persistence_level: this.analyzePersistenceLevel(vulnerability, exploitationResult),
            exploitation_attempts: this.countExploitationAttempts(exploitationResult),
            success_rate: this.calculateSuccessRate(exploitationResult),
            consistency_analysis: this.analyzeConsistency(exploitationResult),
            environmental_factors: this.analyzeEnvironmentalFactors(vulnerability, exploitationResult)
        };

        return persistenceAnalysis;
    }

    // توليد وصف ديناميكي بناءً على النتائج الحقيقية
    generateDynamicDescription(vulnerability, exploitationResult) {
        if (!exploitationResult || !exploitationResult.poc) {
            return `تم اكتشاف ${vulnerability.name} في النظام المستهدف. هذه الثغرة تتطلب تحليل إضافي لتحديد التأثير الكامل.`;
        }

        const poc = exploitationResult.poc;
        let description = `تم اكتشاف واستغلال ${vulnerability.name} بنجاح في النظام المستهدف. `;

        if (poc.success) {
            description += `تم تأكيد وجود الثغرة من خلال الاستغلال الفعلي. `;
            
            if (poc.data_accessed) {
                description += `تم الوصول إلى بيانات حساسة من خلال هذه الثغرة. `;
            }
            
            if (poc.code_executed) {
                description += `تم تنفيذ كود خبيث بنجاح من خلال هذه الثغرة. `;
            }
            
            if (poc.evidence && poc.evidence.includes('error')) {
                description += `ظهرت رسائل خطأ تؤكد وجود الثغرة. `;
            }
        }

        return description;
    }

    // استخراج الـ payloads المستخدمة
    extractPayloadsUsed(vulnerability, exploitationResult) {
        const payloads = [];
        
        if (exploitationResult && exploitationResult.poc && exploitationResult.poc.payload_used) {
            payloads.push(exploitationResult.poc.payload_used);
        }
        
        // إضافة payloads افتراضية بناءً على نوع الثغرة
        const vulnType = vulnerability.name.toLowerCase();
        if (vulnType.includes('sql injection')) {
            payloads.push("' OR '1'='1", "'; DROP TABLE users; --", "' UNION SELECT * FROM information_schema.tables --");
        } else if (vulnType.includes('xss')) {
            payloads.push("<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "javascript:alert('XSS')");
        } else if (vulnType.includes('command injection')) {
            payloads.push("; ls -la", "| whoami", "&& cat /etc/passwd");
        }
        
        return payloads;
    }

    // استخراج رسائل الخطأ
    extractErrorMessages(exploitationResult) {
        const errorMessages = [];
        
        if (exploitationResult && exploitationResult.poc) {
            const poc = exploitationResult.poc;
            
            if (poc.evidence) {
                // البحث عن رسائل خطأ SQL
                if (poc.evidence.includes('mysql') || poc.evidence.includes('sql') || poc.evidence.includes('syntax error')) {
                    errorMessages.push("You have an error in your SQL syntax");
                }
                
                // البحث عن رسائل خطأ PHP
                if (poc.evidence.includes('php') || poc.evidence.includes('warning') || poc.evidence.includes('fatal error')) {
                    errorMessages.push("PHP Warning: mysql_fetch_array()");
                }
                
                // البحث عن رسائل خطأ عامة
                if (poc.evidence.includes('error') || poc.evidence.includes('exception')) {
                    errorMessages.push("Internal Server Error");
                }
            }
            
            if (poc.response_snippet) {
                // تحليل نص الاستجابة للبحث عن رسائل خطأ
                const response = poc.response_snippet.toLowerCase();
                if (response.includes('error') || response.includes('exception') || response.includes('warning')) {
                    errorMessages.push("تم اكتشاف رسائل خطأ في استجابة الخادم");
                }
            }
        }
        
        return errorMessages;
    }
}

// تصدير الكلاس للاستخدام العام
if (typeof window !== 'undefined') {
    window.TextualImpactAnalyzer = TextualImpactAnalyzer;
}

console.log('✅ تم تحميل TextualImpactAnalyzer v4.0 بنجاح!');
