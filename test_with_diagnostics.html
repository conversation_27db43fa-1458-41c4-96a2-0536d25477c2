<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test With Diagnostics</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>Test With Diagnostics</h1>
    <button onclick="testWithDiagnostics()">Test With Diagnostics</button>
    <div id="results"></div>

    <!-- تحميل ملف التشخيص أولاً -->
    <script src="diagnostic_logger.js"></script>
    
    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function testWithDiagnostics() {
            document.getElementById('results').innerHTML = '';
            log('🔍 بدء الاختبار مع التشخيص المفصل...', 'info');
            
            // تسجيل بداية الاختبار
            diagnosticLogger.info('TEST', 'بدء اختبار النظام مع التشخيص المفصل');
            
            try {
                // Clear cache and load fresh
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                    diagnosticLogger.info('CLEANUP', 'تم حذف BugBountyCore القديم من الذاكرة');
                }
                
                const timestamp = new Date().getTime();
                const scriptUrl = `./assets/modules/bugbounty/BugBountyCore.js?v=${timestamp}&diagnostic=true`;
                
                diagnosticLogger.info('LOADING', 'بدء تحميل BugBountyCore', { url: scriptUrl });
                
                const script = document.createElement('script');
                script.src = scriptUrl;
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        diagnosticLogger.success('LOADING', 'تم تحميل BugBountyCore بنجاح');
                        resolve();
                    };
                    script.onerror = (error) => {
                        diagnosticLogger.error('LOADING', 'فشل في تحميل BugBountyCore', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore بنجاح', 'success');
                diagnosticLogger.success('INIT', 'BugBountyCore متاح في النطاق العام');
                
                // Test creating instance
                diagnosticLogger.info('INIT', 'بدء إنشاء مثيل BugBountyCore');
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                diagnosticLogger.success('INIT', 'تم إنشاء مثيل BugBountyCore بنجاح');
                
                // Test template loading first
                log('🧪 اختبار تحميل القالب مباشرة...', 'info');
                diagnosticLogger.info('TEMPLATE', 'بدء اختبار تحميل القالب');
                
                try {
                    const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                    if (templateResponse.ok) {
                        const templateContent = await templateResponse.text();
                        diagnosticLogger.success('TEMPLATE', 'تم تحميل القالب مباشرة', { 
                            size: templateContent.length,
                            hasVariables: templateContent.includes('{{')
                        });
                        
                        // Set template manually
                        core.reportTemplateHTML = templateContent;
                        log('✅ تم تحميل القالب وحفظه في المثيل', 'success');
                        
                    } else {
                        diagnosticLogger.error('TEMPLATE', 'فشل في تحميل القالب', { 
                            status: templateResponse.status,
                            statusText: templateResponse.statusText
                        });
                    }
                } catch (templateError) {
                    diagnosticLogger.error('TEMPLATE', 'خطأ في تحميل القالب', templateError);
                }
                
                // Test generateFinalComprehensiveReport function
                const testData = {
                    total_vulnerabilities: 3,
                    vulnerabilities: [
                        { name: 'XSS', severity: 'High', description: 'Cross-site scripting vulnerability' },
                        { name: 'SQL Injection', severity: 'Critical', description: 'SQL injection vulnerability' },
                        { name: 'CSRF', severity: 'Medium', description: 'Cross-site request forgery' }
                    ]
                };
                
                log('🧪 اختبار دالة generateFinalComprehensiveReport...', 'info');
                diagnosticLogger.info('REPORT', 'بدء اختبار إنشاء التقرير الرئيسي', testData);
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://example.com');
                    
                    if (report && typeof report === 'string' && report.length > 1000) {
                        log(`✅ تم إنشاء التقرير بنجاح - الحجم: ${report.length} حرف`, 'success');
                        diagnosticLogger.success('REPORT', 'تم إنشاء التقرير الرئيسي بنجاح', {
                            size: report.length,
                            type: typeof report
                        });
                        
                        // Check if variables are replaced
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`❌ متغيرات غير مستبدلة: ${variables.join(', ')}`, 'error');
                            diagnosticLogger.error('REPORT', 'متغيرات غير مستبدلة', { variables });
                        } else {
                            log('🎉 تم استبدال جميع المتغيرات بنجاح!', 'success');
                            diagnosticLogger.success('REPORT', 'تم استبدال جميع المتغيرات بنجاح');
                        }
                        
                        // Check specific content
                        const checks = {
                            hasVulnCount: report.includes('3') && !report.includes('{{TOTAL_VULNERABILITIES}}'),
                            hasTargetUrl: report.includes('https://example.com') && !report.includes('{{TARGET_URL}}'),
                            hasTimestamp: !report.includes('{{TIMESTAMP}}'),
                            hasVulnContent: !report.includes('{{VULNERABILITIES_CONTENT}}')
                        };
                        
                        diagnosticLogger.info('REPORT', 'فحص محتوى التقرير', checks);
                        
                        log(`📊 فحص المحتوى:`, 'info');
                        Object.entries(checks).forEach(([key, value]) => {
                            log(`   - ${key}: ${value ? '✅' : '❌'}`, value ? 'success' : 'error');
                        });
                        
                        const allChecksPass = Object.values(checks).every(check => check);
                        if (allChecksPass) {
                            log('🎉 جميع الفحوصات نجحت!', 'success');
                            diagnosticLogger.success('REPORT', 'جميع فحوصات التقرير الرئيسي نجحت');
                        } else {
                            log('⚠️ بعض الفحوصات فشلت', 'warning');
                            diagnosticLogger.warn('REPORT', 'بعض فحوصات التقرير الرئيسي فشلت', checks);
                        }
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير أو التقرير فارغ', 'error');
                        diagnosticLogger.error('REPORT', 'فشل في إنشاء التقرير الرئيسي', {
                            report: report ? 'موجود' : 'غير موجود',
                            type: typeof report,
                            length: report ? report.length : 0
                        });
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ في إنشاء التقرير: ${reportError.message}`, 'error');
                    diagnosticLogger.error('REPORT', 'خطأ في إنشاء التقرير الرئيسي', {
                        message: reportError.message,
                        stack: reportError.stack
                    });
                }
                
                // Test page report function
                log('🧪 اختبار دالة generatePageHTMLReport...', 'info');
                diagnosticLogger.info('PAGE_REPORT', 'بدء اختبار إنشاء تقرير الصفحة');
                
                try {
                    const pageData = {
                        vulnerabilities: [
                            { name: 'XSS', severity: 'High', description: 'Page XSS vulnerability' }
                        ]
                    };
                    
                    const pageReport = await core.generatePageHTMLReport(pageData, 'https://example.com/page1', 1);
                    
                    if (pageReport && typeof pageReport === 'string' && pageReport.length > 1000) {
                        log(`✅ تم إنشاء تقرير الصفحة بنجاح - الحجم: ${pageReport.length} حرف`, 'success');
                        diagnosticLogger.success('PAGE_REPORT', 'تم إنشاء تقرير الصفحة بنجاح', {
                            size: pageReport.length
                        });
                        
                        const pageVariables = pageReport.match(/{{[^}]+}}/g);
                        if (!pageVariables) {
                            log('✅ تم استبدال جميع متغيرات تقرير الصفحة بنجاح!', 'success');
                            diagnosticLogger.success('PAGE_REPORT', 'تم استبدال جميع متغيرات تقرير الصفحة');
                        } else {
                            log(`❌ متغيرات غير مستبدلة في تقرير الصفحة: ${pageVariables.join(', ')}`, 'error');
                            diagnosticLogger.error('PAGE_REPORT', 'متغيرات غير مستبدلة في تقرير الصفحة', { variables: pageVariables });
                        }
                    } else {
                        log('❌ فشل في إنشاء تقرير الصفحة', 'error');
                        diagnosticLogger.error('PAGE_REPORT', 'فشل في إنشاء تقرير الصفحة', {
                            report: pageReport ? 'موجود' : 'غير موجود',
                            type: typeof pageReport,
                            length: pageReport ? pageReport.length : 0
                        });
                    }
                    
                } catch (pageError) {
                    log(`❌ خطأ في إنشاء تقرير الصفحة: ${pageError.message}`, 'error');
                    diagnosticLogger.error('PAGE_REPORT', 'خطأ في إنشاء تقرير الصفحة', {
                        message: pageError.message,
                        stack: pageError.stack
                    });
                }
                
                // عرض إحصائيات التشخيص
                const stats = diagnosticLogger.getStats();
                log(`📊 إحصائيات التشخيص: ${stats.total} سجل (${stats.errors} أخطاء، ${stats.warnings} تحذيرات، ${stats.success} نجاح)`, 'info');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
                diagnosticLogger.error('GENERAL', 'خطأ عام في الاختبار', {
                    message: error.message,
                    stack: error.stack
                });
            }
        }
    </script>
</body>
</html>
