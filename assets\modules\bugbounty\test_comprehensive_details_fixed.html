<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 اختبار التفاصيل الشاملة المحدثة - Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .details-preview {
            background: #f1f3f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار التفاصيل الشاملة المحدثة</h1>
            <p>Bug Bounty v4.0 - التحقق من الإصلاحات</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🎯 الاختبارات المتاحة</h3>
                <button class="btn" onclick="testComprehensiveDetailsGeneration()">🔬 اختبار إنشاء التفاصيل الشاملة</button>
                <button class="btn" onclick="testReportUsage()">📄 اختبار استخدام التقارير للتفاصيل الشاملة</button>
                <button class="btn" onclick="testVulnerabilitySection()">🚨 اختبار قسم الثغرة الشامل</button>
                <button class="btn" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            </div>

            <div class="stats" id="testStats" style="display: none;">
                <div class="stat-item">
                    <div class="stat-number" id="passedTests">0</div>
                    <div>نجح</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="failedTests">0</div>
                    <div>فشل</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalTests">0</div>
                    <div>المجموع</div>
                </div>
            </div>

            <div id="testResults"></div>
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        let testResults = { passed: 0, failed: 0, total: 0 };

        // تهيئة النظام
        async function initializeSystem() {
            addResult('🔄 تهيئة نظام Bug Bounty v4.0...', 'info');
            
            try {
                if (typeof BugBountyCore !== 'undefined') {
                    bugBountyCore = new BugBountyCore();
                    addResult('✅ تم تهيئة BugBountyCore بنجاح', 'success');
                    return true;
                } else {
                    addResult('❌ BugBountyCore غير متوفر', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في التهيئة: ${error.message}`, 'error');
                return false;
            }
        }

        // إنشاء ثغرة اختبار
        function createTestVulnerability() {
            return {
                name: 'SQL Injection Test',
                type: 'SQL Injection',
                severity: 'High',
                location: 'http://testphp.vulnweb.com/artists.php?artist=1',
                parameter: 'artist',
                payload: "' OR '1'='1' --",
                evidence: 'SQL injection confirmed through error message',
                url: 'http://testphp.vulnweb.com/artists.php',
                method: 'GET',
                response_code: '200',
                cvss_score: '10.0'
            };
        }

        // إضافة نتيجة
        function addResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            // التمرير للأسفل
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('testStats').style.display = 'flex';
        }

        // اختبار إنشاء التفاصيل الشاملة
        async function testComprehensiveDetailsGeneration() {
            addResult('🔬 بدء اختبار إنشاء التفاصيل الشاملة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            try {
                const testVuln = createTestVulnerability();
                
                // التحقق من وجود الدالة
                if (typeof bugBountyCore.extractRealDataFromDiscoveredVulnerability !== 'function') {
                    addResult('⚠️ دالة extractRealDataFromDiscoveredVulnerability غير موجودة', 'warning');
                    return false;
                }
                
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                addResult('📊 تم استخراج البيانات الحقيقية', 'success');

                // التحقق من وجود الدالة الشاملة
                if (typeof bugBountyCore.generateComprehensiveDetailsFromRealData !== 'function') {
                    addResult('⚠️ دالة generateComprehensiveDetailsFromRealData غير موجودة', 'warning');
                    return false;
                }

                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                if (comprehensiveDetails && typeof comprehensiveDetails === 'object') {
                    addResult('✅ تم إنشاء التفاصيل الشاملة بنجاح', 'success');
                    addResult(`   الأقسام: ${Object.keys(comprehensiveDetails).join(', ')}`, 'info');
                    
                    // التحقق من الأقسام المطلوبة
                    const requiredSections = ['technical_details', 'impact_analysis', 'exploitation_results', 'interactive_dialogue', 'evidence'];
                    const missingSections = requiredSections.filter(section => !comprehensiveDetails[section]);
                    
                    if (missingSections.length === 0) {
                        addResult('✅ جميع الأقسام المطلوبة موجودة', 'success');
                        testResults.passed++;
                    } else {
                        addResult(`⚠️ أقسام مفقودة: ${missingSections.join(', ')}`, 'warning');
                        testResults.failed++;
                    }
                    
                    // عرض عينة من التفاصيل
                    const preview = document.createElement('div');
                    preview.className = 'details-preview';
                    preview.textContent = JSON.stringify(comprehensiveDetails, null, 2).substring(0, 1000) + '...';
                    document.getElementById('testResults').appendChild(preview);
                    
                    testResults.total++;
                    updateStats();
                    return true;
                } else {
                    addResult('❌ فشل في إنشاء التفاصيل الشاملة', 'error');
                    addResult(`   النوع المُرجع: ${typeof comprehensiveDetails}`, 'error');
                    testResults.failed++;
                    testResults.total++;
                    updateStats();
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار التفاصيل الشاملة: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
                testResults.failed++;
                testResults.total++;
                updateStats();
                return false;
            }
        }

        // اختبار استخدام التقارير للتفاصيل الشاملة
        async function testReportUsage() {
            addResult('📄 بدء اختبار استخدام التقارير للتفاصيل الشاملة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            try {
                const testVuln = createTestVulnerability();
                
                // التحقق من وجود الدوال المطلوبة
                const requiredFunctions = [
                    'extractRealDataFromDiscoveredVulnerability',
                    'generateComprehensiveDetailsFromRealData',
                    'formatComprehensiveVulnerabilitySection'
                ];
                
                for (const funcName of requiredFunctions) {
                    if (typeof bugBountyCore[funcName] !== 'function') {
                        addResult(`⚠️ دالة ${funcName} غير موجودة`, 'warning');
                        testResults.failed++;
                        testResults.total++;
                        updateStats();
                        return false;
                    }
                }
                
                // إضافة التفاصيل الشاملة للثغرة
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                addResult('📊 تم إنشاء التفاصيل الشاملة للثغرة', 'success');
                
                // اختبار formatComprehensiveVulnerabilitySection
                const vulnSection = await bugBountyCore.formatComprehensiveVulnerabilitySection(testVuln, 1, testVuln.location);
                
                if (vulnSection && vulnSection.length > 100) {
                    addResult('✅ تم إنشاء قسم الثغرة الشامل بنجاح', 'success');
                    addResult(`   الحجم: ${vulnSection.length} حرف`, 'info');
                    
                    // فحص المحتوى للتأكد من عدم وجود نصوص عامة
                    const genericPatterns = [
                        'تأثير متغير حسب السياق',
                        'مستخرجة تلقائياً من البرومبت',
                        'payload_example',
                        'غير محدد',
                        'لا توجد أدلة'
                    ];
                    
                    const foundGeneric = genericPatterns.filter(pattern => vulnSection.includes(pattern));
                    
                    if (foundGeneric.length === 0) {
                        addResult('✅ لا يوجد محتوى عام - التفاصيل مخصصة', 'success');
                        testResults.passed++;
                    } else {
                        addResult('⚠️ تم العثور على محتوى عام في التقرير:', 'warning');
                        foundGeneric.forEach(pattern => {
                            addResult(`   - ${pattern}`, 'info');
                        });
                        testResults.failed++;
                    }
                    
                    testResults.total++;
                    updateStats();
                    return true;
                } else {
                    addResult('❌ فشل في إنشاء قسم الثغرة', 'error');
                    addResult(`   النوع المُرجع: ${typeof vulnSection}`, 'error');
                    addResult(`   الحجم: ${vulnSection ? vulnSection.length : 0}`, 'error');
                    testResults.failed++;
                    testResults.total++;
                    updateStats();
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار التقارير: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
                testResults.failed++;
                testResults.total++;
                updateStats();
                return false;
            }
        }

        // اختبار قسم الثغرة الشامل
        async function testVulnerabilitySection() {
            addResult('🚨 بدء اختبار قسم الثغرة الشامل...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            try {
                const testVuln = createTestVulnerability();
                
                // إضافة comprehensive_details
                const realData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                // اختبار generateComprehensiveVulnerabilitiesContentUsingExistingFunctions
                if (typeof bugBountyCore.generateComprehensiveVulnerabilitiesContentUsingExistingFunctions === 'function') {
                    const content = await bugBountyCore.generateComprehensiveVulnerabilitiesContentUsingExistingFunctions([testVuln], 'http://testphp.vulnweb.com');
                    
                    if (content && content.length > 100) {
                        addResult('✅ تم إنشاء محتوى الثغرات الشامل بنجاح', 'success');
                        addResult(`   الحجم: ${content.length} حرف`, 'info');
                        
                        if (testVuln.comprehensive_details) {
                            addResult('✅ الثغرة تحتوي على comprehensive_details', 'success');
                            testResults.passed++;
                        } else {
                            addResult('⚠️ الثغرة لا تحتوي على comprehensive_details', 'warning');
                            testResults.failed++;
                        }
                        
                        testResults.total++;
                        updateStats();
                        return true;
                    } else {
                        addResult('❌ فشل في إنشاء محتوى الثغرات', 'error');
                        testResults.failed++;
                        testResults.total++;
                        updateStats();
                        return false;
                    }
                } else {
                    addResult('⚠️ دالة generateComprehensiveVulnerabilitiesContentUsingExistingFunctions غير موجودة', 'warning');
                    testResults.failed++;
                    testResults.total++;
                    updateStats();
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار قسم الثغرة: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
                testResults.failed++;
                testResults.total++;
                updateStats();
                return false;
            }
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            addResult('🚀 تشغيل جميع الاختبارات...', 'info');
            
            // إعادة تعيين النتائج
            testResults = { passed: 0, failed: 0, total: 0 };
            document.getElementById('testResults').innerHTML = '';
            
            const tests = [
                { name: 'إنشاء التفاصيل الشاملة', func: testComprehensiveDetailsGeneration },
                { name: 'استخدام التقارير', func: testReportUsage },
                { name: 'قسم الثغرة الشامل', func: testVulnerabilitySection }
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                addResult(`🧪 اختبار ${i + 1}/${tests.length}: ${test.name}`, 'info');
                await test.func();
                
                // انتظار قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // النتائج النهائية
            addResult(`📊 النتائج النهائية: ${testResults.passed}/${testResults.total} اختبار نجح`, 
                     testResults.passed === testResults.total ? 'success' : 'warning');
            
            if (testResults.failed > 0) {
                addResult('⚠️ بعض الاختبارات فشلت - يحتاج إصلاح', 'warning');
            } else {
                addResult('🎉 جميع الاختبارات نجحت!', 'success');
            }
        }

        // تهيئة تلقائية عند تحميل الصفحة
        window.addEventListener('load', async () => {
            await initializeSystem();
        });
    </script>
</body>
</html>
