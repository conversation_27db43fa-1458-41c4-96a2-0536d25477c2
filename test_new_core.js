/**
 * Test New Core - للتأكد من أن التحديثات تعمل
 */

console.log('🔧 تحميل Test New Core - النسخة المحدثة...');
console.log('✅ تم إزالة القوالب المدمجة وإضافة loadReportTemplate');

class TestBugBountyCore {
    constructor() {
        console.log('🏗️ إنشاء مثيل TestBugBountyCore...');
        this.isActive = false;
        this.currentTarget = null;
        this.reportTemplateHTML = null;
        
        console.log('✅ تم إنشاء المثيل بنجاح');
    }

    // 🔧 دالة تحميل القالب الأصلي من الملف - الحل النهائي المطلوب
    async loadReportTemplate() {
        console.log('🎯 دالة loadReportTemplate تم تعريفها وتشغيلها بنجاح!');
        try {
            console.log('🔄 تحميل قالب التقرير من الملف الأصلي...');

            // قائمة المسارات المحتملة للقالب
            const templatePaths = [
                './assets/modules/bugbounty/report_template.html',
                '/assets/modules/bugbounty/report_template.html',
                'assets/modules/bugbounty/report_template.html',
                '../assets/modules/bugbounty/report_template.html'
            ];

            let response = null;
            let successfulPath = null;

            // تجربة كل مسار حتى نجد واحد يعمل
            for (const path of templatePaths) {
                try {
                    console.log(`🔍 محاولة تحميل القالب من: ${path}`);
                    response = await fetch(path);

                    if (response.ok) {
                        successfulPath = path;
                        console.log(`✅ تم العثور على القالب في: ${path}`);
                        break;
                    } else {
                        console.log(`⚠️ فشل في المسار ${path}: ${response.status} ${response.statusText}`);
                    }
                } catch (pathError) {
                    console.log(`⚠️ خطأ في المسار ${path}: ${pathError.message}`);
                }
            }

            if (!response || !response.ok) {
                throw new Error('فشل في تحميل القالب من جميع المسارات المحتملة');
            }

            this.reportTemplateHTML = await response.text();

            if (!this.reportTemplateHTML || this.reportTemplateHTML.length < 100) {
                throw new Error('القالب المحمل فارغ أو غير صالح');
            }

            console.log(`✅ تم تحميل قالب التقرير بنجاح من ${successfulPath}`);
            console.log(`📄 حجم القالب: ${this.reportTemplateHTML.length} حرف`);
            console.log('💾 تم حفظ القالب في this.reportTemplateHTML');

        } catch (error) {
            console.error('❌ خطأ في تحميل قالب التقرير:', error);
            this.reportTemplateHTML = null;
            throw error;
        }
    }

    // دالة اختبار بسيطة
    testMethod() {
        console.log('✅ دالة الاختبار تعمل بنجاح!');
        return 'success';
    }
}

// تصدير الكلاس
if (typeof window !== 'undefined') {
    window.TestBugBountyCore = TestBugBountyCore;
    console.log('✅ تم تصدير TestBugBountyCore للنطاق العام');
}

console.log('🎉 تم تحميل Test New Core بالكامل!');
