# 📊 التحقق من استخدام الدوال الشاملة في التقارير - Bug Bounty v4.0
# PowerShell Script للتحقق السريع من استخدام جميع الدوال

Write-Host "🚀 بدء التحقق من استخدام الدوال الشاملة في Bug Bounty v4.0..." -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray

# مسار الملف الرئيسي
$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "❌ لم يتم العثور على ملف BugBountyCore.js" -ForegroundColor Red
    exit 1
}

Write-Host "✅ تم العثور على ملف BugBountyCore.js" -ForegroundColor Green

# قراءة محتوى الملف
$content = Get-Content $bugBountyFile -Raw

# قائمة الدوال الشاملة التفصيلية
$comprehensiveFunctions = @(
    "extractRealDataFromDiscoveredVulnerability",
    "generateComprehensiveDetailsFromRealData", 
    "formatComprehensiveVulnerabilitySection",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealImpactChangesForVulnerability",
    "extractRealEvidenceFromTesting",
    "generateRealDetailedDialogueFromDiscoveredVulnerability",
    "generateInteractiveDialogue",
    "generateRealVisualChangesForVulnerability",
    "generateRealPersistentResultsForVulnerability",
    "extractRealPersistentDataFromDiscoveredVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "extractRealExploitationDataFromDiscoveredVulnerability",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealTechnicalFixesFromDiscoveredVulnerability",
    "generateDynamicExpertAnalysisForVulnerability",
    "extractRealExpertAnalysisFromDiscoveredVulnerability",
    "generateFinalComprehensiveReport",
    "generateComprehensiveVulnerabilitiesContentUsingExistingFunctions",
    "formatSinglePageReport"
)

# الدوال المستهدفة للتحقق من الاستخدام
$targetFunctions = @(
    "generateFinalComprehensiveReport",
    "generateComprehensiveVulnerabilitiesContentUsingExistingFunctions", 
    "generateVulnerabilitiesHTML",
    "formatComprehensiveVulnerabilitySection",
    "formatSinglePageReport"
)

Write-Host "`n📊 فحص وجود الدوال الشاملة..." -ForegroundColor Yellow

$foundFunctions = 0
$totalFunctions = $comprehensiveFunctions.Count

foreach ($func in $comprehensiveFunctions) {
    if ($content -match "async\s+$func\s*\(|$func\s*\(|$func\s*:") {
        Write-Host "✅ $func" -ForegroundColor Green
        $foundFunctions++
    } else {
        Write-Host "❌ $func" -ForegroundColor Red
    }
}

Write-Host "`n📈 نتائج فحص الوجود:" -ForegroundColor Cyan
Write-Host "الدوال الموجودة: $foundFunctions/$totalFunctions" -ForegroundColor $(if ($foundFunctions -eq $totalFunctions) { "Green" } else { "Yellow" })

Write-Host "`n🔍 فحص استخدام الدوال في التقارير..." -ForegroundColor Yellow

# فحص استخدام كل دالة في الدوال المستهدفة
$usageResults = @{}

foreach ($func in $comprehensiveFunctions) {
    $usageResults[$func] = @{
        "MainReport" = $false
        "SeparateReports" = $false
        "UsageCount" = 0
    }
    
    # فحص الاستخدام في كل دالة مستهدفة
    foreach ($target in $targetFunctions) {
        # البحث عن تعريف الدالة المستهدفة
        if ($content -match "(?s)async\s+$target\s*\([^}]+?\{.*?\}") {
            $targetFunctionContent = $matches[0]
            
            # فحص استخدام الدالة داخل الدالة المستهدفة
            if ($targetFunctionContent -match $func) {
                $usageResults[$func]["UsageCount"]++
                
                # تحديد نوع التقرير
                if ($target -match "generateFinalComprehensiveReport|generateComprehensiveVulnerabilitiesContentUsingExistingFunctions|generateVulnerabilitiesHTML") {
                    $usageResults[$func]["MainReport"] = $true
                }
                if ($target -match "formatSinglePageReport|formatComprehensiveVulnerabilitySection") {
                    $usageResults[$func]["SeparateReports"] = $true
                }
            }
        }
    }
}

Write-Host "`n📄 نتائج استخدام الدوال في التقرير الرئيسي:" -ForegroundColor Cyan
$mainReportUsed = 0
foreach ($func in $comprehensiveFunctions) {
    if ($usageResults[$func]["MainReport"]) {
        Write-Host "✅ $func" -ForegroundColor Green
        $mainReportUsed++
    } else {
        Write-Host "❌ $func" -ForegroundColor Red
    }
}

Write-Host "`n📋 نتائج استخدام الدوال في التقارير المنفصلة:" -ForegroundColor Cyan
$separateReportsUsed = 0
foreach ($func in $comprehensiveFunctions) {
    if ($usageResults[$func]["SeparateReports"]) {
        Write-Host "✅ $func" -ForegroundColor Green
        $separateReportsUsed++
    } else {
        Write-Host "❌ $func" -ForegroundColor Red
    }
}

Write-Host "`n🎯 الملخص النهائي:" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

Write-Host "📊 إجمالي الدوال الشاملة: $totalFunctions" -ForegroundColor White
Write-Host "✅ الدوال الموجودة: $foundFunctions" -ForegroundColor Green
Write-Host "📄 المستخدمة في التقرير الرئيسي: $mainReportUsed" -ForegroundColor $(if ($mainReportUsed -gt 15) { "Green" } elseif ($mainReportUsed -gt 10) { "Yellow" } else { "Red" })
Write-Host "📋 المستخدمة في التقارير المنفصلة: $separateReportsUsed" -ForegroundColor $(if ($separateReportsUsed -gt 15) { "Green" } elseif ($separateReportsUsed -gt 10) { "Yellow" } else { "Red" })

$overallUsage = [math]::Round((($mainReportUsed + $separateReportsUsed) / ($totalFunctions * 2)) * 100, 1)
Write-Host "📈 معدل الاستخدام الإجمالي: $overallUsage%" -ForegroundColor $(if ($overallUsage -gt 80) { "Green" } elseif ($overallUsage -gt 60) { "Yellow" } else { "Red" })

# فحص خاص لـ formatComprehensiveVulnerabilitySection
Write-Host "`n🔥 فحص خاص لـ formatComprehensiveVulnerabilitySection:" -ForegroundColor Yellow

if ($content -match "(?s)async\s+formatComprehensiveVulnerabilitySection\s*\([^}]+?\{.*?\}") {
    $formatFunctionContent = $matches[0]
    $functionsInFormat = 0
    
    foreach ($func in $comprehensiveFunctions) {
        if ($func -ne "formatComprehensiveVulnerabilitySection" -and $formatFunctionContent -match $func) {
            $functionsInFormat++
        }
    }
    
    Write-Host "✅ formatComprehensiveVulnerabilitySection تستخدم $functionsInFormat من أصل $($totalFunctions-1) دالة" -ForegroundColor Green
    
    if ($functionsInFormat -gt 15) {
        Write-Host "🎉 ممتاز! معظم الدوال الشاملة مستخدمة في formatComprehensiveVulnerabilitySection" -ForegroundColor Green
    } elseif ($functionsInFormat -gt 10) {
        Write-Host "⚠️ جيد - معظم الدوال مستخدمة مع بعض النقص" -ForegroundColor Yellow
    } else {
        Write-Host "❌ يحتاج تحسين - القليل من الدوال مستخدمة" -ForegroundColor Red
    }
}

Write-Host "`n🎯 الخلاصة النهائية:" -ForegroundColor Cyan
if ($foundFunctions -eq $totalFunctions -and $mainReportUsed -gt 15 -and $separateReportsUsed -gt 15) {
    Write-Host "🎉 ممتاز! جميع الدوال الشاملة التفصيلية موجودة ومستخدمة في التقارير" -ForegroundColor Green
    Write-Host "✅ النظام v4 يعمل بكامل طاقته مع التفاصيل الشاملة" -ForegroundColor Green
} elseif ($foundFunctions -eq $totalFunctions) {
    Write-Host "⚠️ جيد - جميع الدوال موجودة لكن بعضها قد لا يُستخدم بالكامل" -ForegroundColor Yellow
    Write-Host "🔧 يُنصح بمراجعة استخدام الدوال في التقارير" -ForegroundColor Yellow
} else {
    Write-Host "❌ يحتاج تحسين - بعض الدوال مفقودة أو غير مستخدمة" -ForegroundColor Red
    Write-Host "🔧 يجب مراجعة وإصلاح النظام" -ForegroundColor Red
}

Write-Host "`n📁 ملفات التحقق الإضافية:" -ForegroundColor Cyan
Write-Host "📊 report_usage_verification.html - للفحص التفاعلي" -ForegroundColor White
Write-Host "📄 COMPREHENSIVE_FUNCTIONS_USAGE_ANALYSIS.md - للتحليل المفصل" -ForegroundColor White

Write-Host "`n✅ انتهى التحقق!" -ForegroundColor Green
