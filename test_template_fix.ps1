# اختبار إصلاح تحميل القالب - Bug Bounty v4.0
Write-Host "🔧 اختبار إصلاح تحميل القالب الأصلي" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

# 1. فحص وجود الملفات المطلوبة
Write-Host "`n📁 فحص الملفات المطلوبة:" -ForegroundColor Yellow

$coreFile = "assets\modules\bugbounty\BugBountyCore.js"
$templateFile = "assets\modules\bugbounty\report_template.html"

if (Test-Path $coreFile) {
    $coreSize = (Get-Item $coreFile).Length
    Write-Host "✅ BugBountyCore.js موجود - الحجم: $coreSize بايت" -ForegroundColor Green
} else {
    Write-Host "❌ BugBountyCore.js غير موجود" -ForegroundColor Red
    exit 1
}

if (Test-Path $templateFile) {
    $templateSize = (Get-Item $templateFile).Length
    Write-Host "✅ report_template.html موجود - الحجم: $templateSize بايت" -ForegroundColor Green
} else {
    Write-Host "❌ report_template.html غير موجود" -ForegroundColor Red
    exit 1
}

# 2. فحص محتوى BugBountyCore.js للتأكد من التحديثات
Write-Host "`n🔍 فحص التحديثات في BugBountyCore.js:" -ForegroundColor Yellow

$coreContent = Get-Content $coreFile -Raw -Encoding UTF8

# فحص وجود دالة loadReportTemplate الجديدة
if ($coreContent -match "async loadReportTemplate\(\)") {
    Write-Host "✅ دالة loadReportTemplate الجديدة موجودة" -ForegroundColor Green
} else {
    Write-Host "❌ دالة loadReportTemplate الجديدة غير موجودة" -ForegroundColor Red
}

# فحص عدم وجود القالب المدمج
if ($coreContent -match "embeddedTemplate") {
    Write-Host "⚠️ ما زال هناك مراجع للقالب المدمج" -ForegroundColor Yellow
    $embeddedMatches = [regex]::Matches($coreContent, "embeddedTemplate")
    Write-Host "   عدد المراجع: $($embeddedMatches.Count)" -ForegroundColor Yellow
} else {
    Write-Host "✅ تم إزالة جميع مراجع القالب المدمج" -ForegroundColor Green
}

# فحص عدم وجود preloadTemplate
if ($coreContent -match "preloadTemplate") {
    Write-Host "⚠️ ما زال هناك مراجع لـ preloadTemplate" -ForegroundColor Yellow
    $preloadMatches = [regex]::Matches($coreContent, "preloadTemplate")
    Write-Host "   عدد المراجع: $($preloadMatches.Count)" -ForegroundColor Yellow
} else {
    Write-Host "✅ تم إزالة جميع مراجع preloadTemplate" -ForegroundColor Green
}

# فحص استخدام reportTemplateHTML
if ($coreContent -match "this\.reportTemplateHTML") {
    Write-Host "✅ يتم استخدام this.reportTemplateHTML" -ForegroundColor Green
    $reportTemplateMatches = [regex]::Matches($coreContent, "this\.reportTemplateHTML")
    Write-Host "   عدد الاستخدامات: $($reportTemplateMatches.Count)" -ForegroundColor Green
} else {
    Write-Host "❌ لا يتم استخدام this.reportTemplateHTML" -ForegroundColor Red
}

# 3. فحص محتوى القالب
Write-Host "`n📄 فحص محتوى القالب:" -ForegroundColor Yellow

$templateContent = Get-Content $templateFile -Raw -Encoding UTF8

if ($templateContent -match "<!DOCTYPE html>") {
    Write-Host "✅ القالب يحتوي على DOCTYPE صحيح" -ForegroundColor Green
} else {
    Write-Host "❌ القالب لا يحتوي على DOCTYPE" -ForegroundColor Red
}

if ($templateContent -match "تقرير Bug Bounty") {
    Write-Host "✅ القالب يحتوي على العنوان الصحيح" -ForegroundColor Green
} else {
    Write-Host "❌ القالب لا يحتوي على العنوان المطلوب" -ForegroundColor Red
}

if ($templateContent -match "\{\{TARGET_URL\}\}") {
    Write-Host "✅ القالب يحتوي على متغيرات القالب" -ForegroundColor Green
} else {
    Write-Host "❌ القالب لا يحتوي على متغيرات القالب" -ForegroundColor Red
}

# 4. اختبار الخادم
Write-Host "`n🌐 اختبار الخادم:" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/assets/modules/bugbounty/report_template.html" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ الخادم يستجيب بشكل صحيح - كود: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "   حجم الاستجابة: $($response.Content.Length) حرف" -ForegroundColor Green
        
        if ($response.Content -match "<!DOCTYPE html>") {
            Write-Host "✅ محتوى القالب صحيح عبر الخادم" -ForegroundColor Green
        } else {
            Write-Host "❌ محتوى القالب غير صحيح عبر الخادم" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ الخادم يستجيب بكود غير متوقع: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ فشل في الاتصال بالخادم: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   تأكد من تشغيل: python -m http.server 3000" -ForegroundColor Yellow
}

# 5. اختبار JavaScript
Write-Host "`n🧪 اختبار JavaScript:" -ForegroundColor Yellow

try {
    $jsTestResponse = Invoke-WebRequest -Uri "http://localhost:3000/simple_test.html" -UseBasicParsing -TimeoutSec 5
    if ($jsTestResponse.StatusCode -eq 200) {
        Write-Host "✅ صفحة الاختبار متاحة" -ForegroundColor Green
        Write-Host "   يمكنك فتح: http://localhost:3000/simple_test.html" -ForegroundColor Cyan
    } else {
        Write-Host "❌ صفحة الاختبار غير متاحة" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ فشل في الوصول لصفحة الاختبار: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. ملخص النتائج
Write-Host "`n📊 ملخص النتائج:" -ForegroundColor Cyan
Write-Host "=" * 30 -ForegroundColor Gray

$issues = @()

if ($coreContent -notmatch "async loadReportTemplate\(\)") {
    $issues += "دالة loadReportTemplate مفقودة"
}

if ($coreContent -match "embeddedTemplate") {
    $issues += "مراجع القالب المدمج ما زالت موجودة"
}

if ($coreContent -match "preloadTemplate") {
    $issues += "مراجع preloadTemplate ما زالت موجودة"
}

if ($issues.Count -eq 0) {
    Write-Host "🎉 جميع التحديثات تمت بنجاح!" -ForegroundColor Green
    Write-Host "✅ النظام جاهز لاستخدام القالب الأصلي من الملف" -ForegroundColor Green
    Write-Host "`n🚀 الخطوات التالية:" -ForegroundColor Cyan
    Write-Host "1. افتح http://localhost:3000/simple_test.html" -ForegroundColor White
    Write-Host "2. اضغط على 'اختبار بسيط' للتحقق من عمل النظام" -ForegroundColor White
    Write-Host "3. تحقق من وحدة التحكم للتأكد من تحميل القالب بنجاح" -ForegroundColor White
} else {
    Write-Host "⚠️ هناك مشاكل تحتاج إلى إصلاح:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "   - $issue" -ForegroundColor Red
    }
}

Write-Host "`n" -ForegroundColor Gray
