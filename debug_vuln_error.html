<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص خطأ "vuln is not defined"</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص خطأ "vuln is not defined" - Bug Bounty v4.0</h1>
        <p>هذه الصفحة لتحديد مكان الخطأ بدقة في نظام Bug Bounty v4.0</p>

        <div class="test-section">
            <h3>🧪 اختبار تدريجي للدوال</h3>
            <button class="test-button" onclick="testStep1()">1️⃣ اختبار تحميل البرومبت</button>
            <button class="test-button" onclick="testStep2()">2️⃣ اختبار extractAllVulnerabilitiesFromExpandedPrompt</button>
            <button class="test-button" onclick="testStep3()">3️⃣ اختبار extractVulnerabilitiesFromSection</button>
            <button class="test-button" onclick="testStep4()">4️⃣ اختبار generateInteractiveDialogue</button>
            <div id="test-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 اختبار مباشر للدوال المشبوهة</h3>
            <button class="test-button" onclick="testSuspiciousFunctions()">اختبار الدوال المشبوهة</button>
            <div id="suspicious-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 سجل الأخطاء المفصل</h3>
            <div id="error-log" class="result"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testPrompt = '';

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            try {
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة BugBountyCore بنجاح');
                logError('✅ تم تهيئة BugBountyCore بنجاح');
            } catch (error) {
                console.error('❌ فشل في تهيئة BugBountyCore:', error);
                logError(`❌ فشل في تهيئة BugBountyCore: ${error.message}`);
            }
        });

        function logError(message) {
            const errorLog = document.getElementById('error-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            errorLog.textContent += `[${timestamp}] ${message}\n`;
            errorLog.scrollTop = errorLog.scrollHeight;
        }

        // الخطوة 1: اختبار تحميل البرومبت
        async function testStep1() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار تحميل البرومبت...';
            logError('🔄 بدء اختبار تحميل البرومبت...');

            try {
                testPrompt = await bugBountyCore.loadPromptTemplate();
                
                if (testPrompt && testPrompt.length > 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم تحميل البرومبت بنجاح\nطول البرومبت: ${testPrompt.length} حرف`;
                    logError(`✅ تم تحميل البرومبت بنجاح (${testPrompt.length} حرف)`);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ البرومبت فارغ أو لم يتم تحميله';
                    logError('❌ البرومبت فارغ أو لم يتم تحميله');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في تحميل البرومبت: ${error.message}`;
                logError(`❌ خطأ في تحميل البرومبت: ${error.message}\nStack: ${error.stack}`);
            }
        }

        // الخطوة 2: اختبار extractAllVulnerabilitiesFromExpandedPrompt
        async function testStep2() {
            const resultDiv = document.getElementById('test-result');
            
            if (!testPrompt) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ يجب تحميل البرومبت أولاً (الخطوة 1)';
                return;
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار extractAllVulnerabilitiesFromExpandedPrompt...';
            logError('🔄 بدء اختبار extractAllVulnerabilitiesFromExpandedPrompt...');

            try {
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromExpandedPrompt(testPrompt, 'http://testphp.vulnweb.com');
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ نجح extractAllVulnerabilitiesFromExpandedPrompt\nعدد الثغرات: ${vulnerabilities.length}`;
                logError(`✅ نجح extractAllVulnerabilitiesFromExpandedPrompt (${vulnerabilities.length} ثغرات)`);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل extractAllVulnerabilitiesFromExpandedPrompt: ${error.message}`;
                logError(`❌ فشل extractAllVulnerabilitiesFromExpandedPrompt: ${error.message}\nStack: ${error.stack}`);
            }
        }

        // الخطوة 3: اختبار extractVulnerabilitiesFromSection
        async function testStep3() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار extractVulnerabilitiesFromSection...';
            logError('🔄 بدء اختبار extractVulnerabilitiesFromSection...');

            try {
                const testSection = {
                    name: 'test_section',
                    content: 'SQL Injection test XSS test CSRF test'
                };
                
                const vulnerabilities = await bugBountyCore.extractVulnerabilitiesFromSection(testSection, 'http://testphp.vulnweb.com');
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ نجح extractVulnerabilitiesFromSection\nعدد الثغرات: ${vulnerabilities.length}`;
                logError(`✅ نجح extractVulnerabilitiesFromSection (${vulnerabilities.length} ثغرات)`);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل extractVulnerabilitiesFromSection: ${error.message}`;
                logError(`❌ فشل extractVulnerabilitiesFromSection: ${error.message}\nStack: ${error.stack}`);
            }
        }

        // الخطوة 4: اختبار generateInteractiveDialogue
        async function testStep4() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار generateInteractiveDialogue...';
            logError('🔄 بدء اختبار generateInteractiveDialogue...');

            try {
                const dialogue = bugBountyCore.generateInteractiveDialogue('SQL Injection', {
                    payload: "' OR '1'='1' --",
                    response: 'Database error revealed',
                    evidence: 'SQL injection confirmed'
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ نجح generateInteractiveDialogue\nطول النتيجة: ${dialogue.length} حرف`;
                logError(`✅ نجح generateInteractiveDialogue (${dialogue.length} حرف)`);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ فشل generateInteractiveDialogue: ${error.message}`;
                logError(`❌ فشل generateInteractiveDialogue: ${error.message}\nStack: ${error.stack}`);
            }
        }

        // اختبار الدوال المشبوهة
        async function testSuspiciousFunctions() {
            const resultDiv = document.getElementById('suspicious-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار الدوال المشبوهة...';
            logError('🔄 بدء اختبار الدوال المشبوهة...');

            let results = [];

            // اختبار generateInteractiveDialogues (للمصفوفة)
            try {
                const testVulns = [{
                    name: 'SQL Injection',
                    type: 'SQL Injection',
                    payload: "' OR '1'='1' --"
                }];
                const dialogues = bugBountyCore.generateInteractiveDialogues(testVulns);
                results.push('✅ generateInteractiveDialogues: نجح');
                logError('✅ generateInteractiveDialogues: نجح');
            } catch (error) {
                results.push(`❌ generateInteractiveDialogues: ${error.message}`);
                logError(`❌ generateInteractiveDialogues: ${error.message}\nStack: ${error.stack}`);
            }

            // اختبار generateVisualChanges
            try {
                const visualChanges = bugBountyCore.generateVisualChanges('SQL Injection', { url: 'http://test.com' });
                results.push('✅ generateVisualChanges: نجح');
                logError('✅ generateVisualChanges: نجح');
            } catch (error) {
                results.push(`❌ generateVisualChanges: ${error.message}`);
                logError(`❌ generateVisualChanges: ${error.message}\nStack: ${error.stack}`);
            }

            // اختبار generateComprehensiveReport
            try {
                const testVulns = [{
                    name: 'SQL Injection',
                    type: 'SQL Injection',
                    severity: 'High'
                }];
                const report = bugBountyCore.generateComprehensiveReport(testVulns, 'http://test.com');
                results.push('✅ generateComprehensiveReport: نجح');
                logError('✅ generateComprehensiveReport: نجح');
            } catch (error) {
                results.push(`❌ generateComprehensiveReport: ${error.message}`);
                logError(`❌ generateComprehensiveReport: ${error.message}\nStack: ${error.stack}`);
            }

            resultDiv.className = 'result success';
            resultDiv.textContent = results.join('\n');
        }
    </script>
</body>
</html>
